import requests
import redis
from config_file import config

config_env = config.env
url_total_connect = config_env['opshub_api']['url']
redis_ip = config_env['opshub_api']['redis']['ip']
redis_port = config_env['opshub_api']['redis']['port']
redis_db = config_env['opshub_api']['redis']['db']
redis_pwd = config_env['opshub_api']['redis']['pwd']
admin_pwd = config_env['opshub_api']['admin_pwd']


def verification_code():
    # 定义验证码接口的URL
    url = url_total_connect + '/env-admin/captchaImage'

    try:
        # 发送GET请求获取验证码
        response = requests.get(url=url).json()
        # 检查请求是否成功
        if response.get('code') == 200 or response.get('code') == '200':
            return response.get('uuid')

    except requests.exceptions.RequestException as e:
        # 记录请求异常错误
        error_message = f"请求验证码接口时发生错误: {e}"
        print(error_message)


def replace_parentheses_with_backslash(input_string):
    # 将 ( 替换为 \
    result_string = input_string.replace('(', '//')
    # 将 ) 替换为 \
    result_string = result_string.replace(')', '//')
    return result_string


def connect_to_redis_remote():
    try:
        # 创建Redis连接对象
        redis_client = redis.Redis(
            host=redis_ip, port=redis_port, db=redis_db, password=redis_pwd)
        # 测试连接是否成功
        redis_client.ping()
        print("连接远程Redis数据库成功！")
        return redis_client
    except Exception as e:
        print("连接远程Redis数据库失败:", e)
        return None


def get_code_value():
    rd_clint = connect_to_redis_remote()
    uuid = verification_code()
    try:
        byte_str = rd_clint.get('captcha_codes:' + uuid)
        decoded_str = byte_str.decode('utf-8')
        # 去除引号
        result_str = decoded_str.strip('"')
        rd_clint.connection_pool.disconnect()
        return result_str, uuid
    except Exception as e:
        print("获取token失败", e)
        rd_clint.connection_pool.disconnect()


def login_get_token():
    url = url_total_connect + '/env-admin/login'
    code, uuid = get_code_value()
    json = {'code': code,
            'password': admin_pwd,
            'username': 'admin', 'uuid': uuid}
    try:
        response = requests.post(url=url, json=json).json()
        if response.get('code') == 200 or response.get('code') == '200':
            return response.get('token')
    except requests.exceptions.RequestException as e:
        # 记录请求异常错误
        error_message = f"请求登录接口时发生错误: {e}"
        print(error_message)


def post(json=None, query=None, url=None, connect=None):
    if json:
        try:
            # 发送GET请求获取验证码
            response = connect.post(url=url, json=json).json()
            print(response)
            # 检查请求是否成功
            if response.get('code') == 200 or response.get('code') == '200':
                return response
        except requests.exceptions.RequestException as e:
            # 记录请求异常错误
            error_message = f"请求验证码接口时发生错误: {e}"
            print(error_message)

    if query:
        try:
            # 发送GET请求获取验证码
            response = connect.post(url=url, params=query).json()

            # 检查请求是否成功
            if response.get('code') == 200 or response.get('code') == '200':
                return response
        except requests.exceptions.RequestException as e:
            # 记录请求异常错误
            error_message = f"请求验证码接口时发生错误: {e}"
            print(error_message)

    else:
        try:
            # 发送GET请求获取验证码
            response = connect.post(url=url).json()

            # 检查请求是否成功
            if response.get('code') == 200 or response.get('code') == '200':
                return response
        except requests.exceptions.RequestException as e:
            # 记录请求异常错误
            error_message = f"请求接口口时发生错误: {e}"
            print(error_message)


def get(json=None, query=None, url=None, connect=None):
    if json:
        try:
            # 发送GET请求获取验证码
            response = connect.get(url=url, json=json).json()

            # 检查请求是否成功
            if response.get('code') == 200 or response.get('code') == '200':
                return response
            else:
                raise requests.exceptions.RequestException(
                    response.get('code'))
        except requests.exceptions.RequestException as e:
            # 记录请求异常错误
            error_message = f"请求验证码接口时发生错误: {e}"
            print(error_message)

    if query:
        try:
            # 发送GET请求获取验证码
            response = connect.get(url=url, params=query).json()

            # 检查请求是否成功
            if response.get('code') == 200 or response.get('code') == '200':
                return response
            else:
                raise requests.exceptions.RequestException(
                    response.get('code'))
        except requests.exceptions.RequestException as e:
            # 记录请求异常错误
            error_message = f"请求验证码接口时发生错误: {e}"
            print(error_message)

    else:
        try:
            # 发送GET请求获取验证码
            response = connect.get(url=url).json()

            # 检查请求是否成功
            if response.get('code') == 200 or response.get('code') == '200':
                return response
            else:
                raise requests.exceptions.RequestException(
                    response.get('code'))
        except requests.exceptions.RequestException as e:
            # 记录请求异常错误
            error_message = f"请求接口口时发生错误: {e}"
            print(error_message)


def create_session():
    session = requests.session()
    token = login_get_token()
    session.headers.update({
        'Authorization': 'Bearer ' + token
    })
    return session


# 修改执行人
def update_task_executor(task_id, old_executor_id, new_executor_id):
    session = create_session()
    url = url_total_connect + \
        '/opshub-api/dispatch/dispatchSchedulingPlan/update_task_executor'
    json = {'taskId': task_id, 'oldExecutorId': old_executor_id,
            'newExecutorId': new_executor_id}
    return post(json=json, url=url, connect=session)


# 修改任务时间
def update_task_time(task_id, executor_id, old_start_time, new_start_time, old_end_time, new_end_time):
    session = create_session()
    url = url_total_connect + \
        '/opshub-api/dispatch/dispatchSchedulingPlan/update_task_time'
    json = {'taskId': task_id, 'executorId': executor_id,
            'oldStartTime': old_start_time, 'newStartTime': new_start_time, 'oldEndTime': old_end_time, 'newEndTime': new_end_time}
    return post(json=json, url=url, connect=session)


def get_alarm_real_time_data(begin_time, end_time, alarm_id, device_id):
    session = create_session()
    begin_time = begin_time.replace(" ", "T")
    end_time = end_time.replace(" ", "T")
    url = url_total_connect + \
        '/opshub-api/device/devicepointmanager/history'
    json = {'beginTime': begin_time, 'endTime': end_time,
            'deviceId': device_id}
    return post(json=json, url=url, connect=session)


if __name__ == "__main__":
    print(1111)
    print(update_task_executor(1721, 33, 42))
    print(1111)
