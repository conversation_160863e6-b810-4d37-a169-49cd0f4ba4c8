#!/usr/bin/env python3
"""
测试滤池处理器重构后的功能
验证条件保存和框线标注功能是否正确
"""

import cv2
import logging
import sys
import os
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from server.utils.handlers.filter_handler import FilterHandler
from server.utils.video_processor import VideoFrameProcessor

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_filter_handler():
    """测试滤池处理器功能"""
    
    # 模拟配置
    config = {
        'storage': {
            'paths': {
                'base_dataset': 'test_dataset'
            },
            'image_processing': {
                'save_intermediate_images': False
            }
        },
        'coverage_levels': {
            'thresholds': {'low': 25, 'medium': 50, 'high': 100},
            'levels': {'low': 'LOW', 'medium': 'MEDIUM', 'high': 'HIGH'}
        },
        'yolo_model_filter': 'llms/models/yolo/best-filter.pt',
        'yolo_save_result': {
            'filter': True,  # 启用保存以便测试
            'bucket_dipper': True,
            'region_counter': True
        }
    }
    
    try:
        # 创建主处理器实例
        logging.info("创建VideoFrameProcessor实例...")
        processor = VideoFrameProcessor(config)
        
        # 创建滤池处理器实例
        logging.info("创建FilterHandler实例...")
        filter_handler = FilterHandler(processor)
        
        # 创建测试目录
        test_save_dir = Path("test_output")
        test_save_dir.mkdir(exist_ok=True)
        
        # 创建测试图像（模拟视频帧）
        logging.info("创建测试图像...")

        # 尝试读取现有的测试图像
        test_image_paths = [
            "assets/frame_4010_2024_12_06_17_48_30.jpg",
            "assets/filter_aeration_failure_images.png",
            "assets/2.jpg"
        ]

        test_frame = None
        for image_path in test_image_paths:
            try:
                test_frame = cv2.imread(image_path)
                if test_frame is not None:
                    logging.info(f"使用现有的测试图像: {image_path}")
                    break
            except:
                continue

        if test_frame is None:
            # 如果没有测试图像，创建一个简单的测试图像
            test_frame = cv2.rectangle(
                cv2.zeros((900, 1600, 3), dtype=cv2.uint8),
                (100, 100), (500, 500), (255, 255, 255), -1
            )
            # 添加一些模拟的水体纹理
            cv2.circle(test_frame, (300, 300), 50, (100, 150, 200), -1)
            cv2.circle(test_frame, (400, 250), 30, (120, 170, 220), -1)
            logging.info("使用生成的测试图像")
        
        # 测试参数
        frame_count = 1
        camera_id = "test_camera"
        sensor_data = {}
        threshold = 50.0
        system_type = "system_prompt_filter1"  # 使用滤池系统类型
        current_time = datetime.now()
        
        # 执行测试
        logging.info("开始测试滤池处理器...")
        result = filter_handler.process_frame(
            frame=test_frame,
            frame_count=frame_count,
            save_dir=test_save_dir,
            camera_id=camera_id,
            sensor_data=sensor_data,
            threshold=threshold,
            system_type=system_type,
            standard_image_path=None,
            current_time=current_time
        )
        
        # 解析结果
        coverage_float, analysis_result, alarm_status, is_abnormal, frame_path, analysis_advice, failure_types = result
        
        # 输出测试结果
        logging.info("=" * 50)
        logging.info("测试结果:")
        logging.info(f"覆盖率: {coverage_float}")
        logging.info(f"分析结果: {analysis_result}")
        logging.info(f"警报状态: {alarm_status}")
        logging.info(f"是否异常: {is_abnormal}")
        logging.info(f"图片路径: {frame_path}")
        logging.info(f"分析建议: {analysis_advice}")
        logging.info(f"故障类型: {failure_types}")
        logging.info("=" * 50)
        
        # 验证功能
        if frame_path:
            logging.info(f"✅ 检测到故障，已保存图片: {frame_path}")
            if Path(frame_path).exists():
                logging.info("✅ 图片文件确实存在")
            else:
                logging.error("❌ 图片文件不存在")
        else:
            logging.info("✅ 未检测到故障，未保存图片")
        
        if failure_types:
            logging.info(f"✅ 故障类型检测正常: {failure_types}")
        
        logging.info("测试完成！")
        return True
        
    except Exception as e:
        logging.error(f"测试过程中发生错误: {str(e)}", exc_info=True)
        return False

if __name__ == "__main__":
    success = test_filter_handler()
    if success:
        print("✅ 滤池处理器测试通过")
    else:
        print("❌ 滤池处理器测试失败")
        sys.exit(1)
