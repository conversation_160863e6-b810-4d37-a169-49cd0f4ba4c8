"""
运行的主程序
"""
import logging
import threading
import time
from concurrent.futures import Thread<PERSON>oolExecutor
from datetime import datetime
from pathlib import Path
from typing import Dict, List
from config_file import config
import cv2
import os
from server.remote.device import get_all_data_by_process_device_data, get_device_list, process_device_data
from server.utils.env_manager import EnvManager
from server.utils.logger import setup_logging
from server.utils.path_manager import PathManager
from server.utils.cleanup import DataCleanupManager

# 配置日志


class VideoMonitoringSystem:
    def __init__(self):
        """初始化视频监控系统"""
        # 初始化路径管理器
        self.path_manager = PathManager()

        self.config = config.env

        # 使用路径管理器获取其他路径
        self.base_dataset_path = self.path_manager.get_path('opshub_dataset')

        # 从配置文件加载系统参数
        api_config = self.config['system']['api']
        retry_config = self.config['system']['retry']
        thread_config = self.config['system']['thread_pool']
        storage_config = self.config['storage']
        cleanup_config = self.config['cleanup']
        self.image_retention_config = self.config.get(
            'image_retention', {'max_images': 2})  # 默认保留2张

        # 设置类常量
        self.MAX_RETRIES = retry_config['max_retries']
        self.RETRY_DELAY = retry_config['delay']
        self.MAX_WORKERS = thread_config['max_workers']
        self.API_TIMEOUT = api_config['timeout']
        self.IMAGE_RETENTION_DAYS = cleanup_config['image_retention_days']
        self.CLEANUP_MONTHS = cleanup_config['cleanup_months']
        self.CLEANUP_DAY = cleanup_config['cleanup_day']
        self.MAX_IMAGES_TO_KEEP = self.image_retention_config.get(
            'max_images', 2)
        self.CAPTURE_INTERVAL = 60  # 默认每隔5秒抓取一张图片

        # 验证并设置API URL
        self.check_interval = float(api_config['check_interval'])

        # 初始化其他属性
        self.is_api_running = False
        self.camera_threads: Dict[str, threading.Thread] = {}
        self.camera_captures: Dict[str, cv2.VideoCapture] = {}
        self.camera_executors: Dict[str, ThreadPoolExecutor] = {}
        self.stop_flags: Dict[str, bool] = {}

        # 设置路径
        self.system_running = True

        # 初始化清理管理器
        self.cleanup_manager = DataCleanupManager(self.config)

    def cleanup_old_images_for_camera(self, base_dir: Path, camera_id: str):
        """清理每个摄像头超过保留数量的旧图片"""
        camera_dir = base_dir / camera_id
        if camera_dir.exists() and camera_dir.is_dir():
            image_files = sorted([f for f in camera_dir.iterdir(
            ) if f.is_file()], key=lambda f: f.stat().st_mtime, reverse=True)
            if len(image_files) > self.MAX_IMAGES_TO_KEEP:
                for file_to_delete in image_files[self.MAX_IMAGES_TO_KEEP:]:
                    try:
                        os.remove(str(file_to_delete))
                        logging.info(f"删除旧图片: {file_to_delete}")
                    except Exception as e:
                        logging.error(f"删除文件 {file_to_delete} 失败: {e}")

    def process_single_camera(self, camera_config: dict):
        """
        处理单个摄像头的视频流，每次获取一张图片并保存
        :param camera_config: 摄像头配置信息
        """
        camera_id = camera_config['camera_id']
        video_paths = camera_config['video_path'] if isinstance(camera_config['video_path'], list) else [
            camera_config['video_path']]

        try:
            with ThreadPoolExecutor(max_workers=1) as executor:  # 对于单张图片获取，一个worker足够
                self.camera_executors[camera_id] = executor
                config_msg = (f"摄像头配置信息 - ID: {camera_id}, "
                              f"视频路径: {video_paths}, "
                              f"抓取间隔: {self.CAPTURE_INTERVAL}秒")
                print(f"{time.strftime('%Y-%m-%d %H:%M:%S')} {config_msg}")
                logging.info(config_msg)

                while not self.stop_flags[camera_id]:
                    try:
                        if not self.check_api_status():
                            logging.warning(
                                f"摄像头 {camera_id}: API服务器已停止，暂停图片获取")
                            time.sleep(self.check_interval)
                            continue

                        cap = self.camera_captures.get(camera_id)
                        if cap is None or not cap.isOpened():
                            if cap is not None:
                                cap.release()  # 确保旧的 capture 被释放

                            connect_msg = f"正在连接摄像头 {camera_id} - 视频路径: {video_paths[-1]}"
                            print(
                                f"{time.strftime('%Y-%m-%d %H:%M:%S')} {connect_msg}")
                            logging.info(connect_msg)

                            cap = cv2.VideoCapture(video_paths[-1])
                            if not cap.isOpened():
                                error_msg = f"摄像头 {camera_id}: 无法打开视频流 - 路径: {video_paths[-1]}"
                                print(
                                    f"{time.strftime('%Y-%m-%d %H:%M:%S')} {error_msg}")
                                logging.error(error_msg)
                                time.sleep(5)
                                continue

                            self.camera_captures[camera_id] = cap

                        ret, frame = cap.read()
                        if not ret:
                            logging.warning(
                                f"摄像头 {camera_id}: 视频流读取失败，尝试重新连接...")
                            cap.release()
                            self.camera_captures[camera_id] = None
                            time.sleep(5)
                            continue

                        current_time = datetime.now()
                        save_dir_parent = Path(f"{self.base_dataset_path}")
                        save_dir = save_dir_parent / camera_id
                        save_dir.mkdir(parents=True, exist_ok=True)
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
                        frame_path = str(save_dir / f"{timestamp}.jpg")
                        cv2.imwrite(frame_path, frame)
                        logging.info(f"摄像头 {camera_id}: 保存图片到 {frame_path}")
                        self.cleanup_old_images_for_camera(
                            save_dir_parent, camera_id)

                        time.sleep(self.CAPTURE_INTERVAL)  # 等待一段时间再获取下一张图片

                    except Exception as e:
                        logging.error(f"摄像头 {camera_id} 处理时发生错误: {str(e)}")
                        if camera_id in self.camera_captures:
                            try:
                                self.camera_captures[camera_id].release()
                                self.camera_captures[camera_id] = None
                            except:
                                pass
                        time.sleep(5)

        except Exception as e:
            logging.error(f"摄像头 {camera_id} 线程发生错误: {str(e)}")
        finally:
            # 确保在线程结束时释放资源
            if camera_id in self.camera_captures:
                try:
                    self.camera_captures[camera_id].release()
                    self.camera_captures[camera_id] = None
                except:
                    pass
            if camera_id in self.camera_executors:
                try:
                    self.camera_executors[camera_id].shutdown(wait=False)
                except:
                    pass

    def start_camera_monitoring(self, camera_config: dict):
        """
        启动单个摄像头的监控

        Args:
            camera_config: 摄像头配置字典，包含:
                - camera_id: 摄像头ID
                - video_path: 视频路径
                - threshold: 覆盖率阈值 (现在会被忽略)
                - frame_interval_all: 帧间隔时间 (现在会被忽略)
                - system_type: 系统类型 (现在会被忽略)
                - video_id: 视频ID (现在会被忽略)
        """
        camera_id = camera_config['camera_id']
        self.stop_flags[camera_id] = False
        thread = threading.Thread(
            target=self.process_single_camera,
            args=(camera_config,),
            name=f"Camera-{camera_id}"
        )
        self.camera_threads[camera_id] = thread
        thread.start()

    def stop_camera_monitoring(self, camera_id: str):
        """
        安全停止单个摄像头的监控

        Args:
            camera_id: 要停止的摄像头ID

        说明:
            - 设置停止标志
            - 等待线程结束
            - 释放摄像头资源
            - 关闭线程池
        """
        try:
            # 1. 首先设置停止标志
            self.stop_flags[camera_id] = True

            # 2. 等待线程结束
            if camera_id in self.camera_threads:
                thread = self.camera_threads[camera_id]
                if thread.is_alive():
                    thread.join(timeout=5)  # 等待最多5秒
                del self.camera_threads[camera_id]

            # 3. 释放摄像头资源
            if camera_id in self.camera_captures:
                cap = self.camera_captures[camera_id]
                if cap is not None:
                    cap.release()
                del self.camera_captures[camera_id]

            # 4. 关闭线程池
            if camera_id in self.camera_executors:
                executor = self.camera_executors[camera_id]
                if executor is not None:
                    executor.shutdown(wait=True)  # 等待所有任务完成
                del self.camera_executors[camera_id]

            logging.info(f"摄像头 {camera_id} 已安全停止")
        except Exception as e:
            logging.error(f"停止摄像头 {camera_id} 监控时发生错误: {str(e)}")

    def safe_cleanup_resources(self):
        """
        安全清理所有系统资源

        执行操作:
            1. 停止所有摄像头监控
            2. 清理所有线程资源
            3. 释放所有摄像头资源
        """
        try:
            for camera_id in list(self.camera_threads.keys()):
                try:
                    self.stop_camera_monitoring(camera_id)
                except Exception as e:
                    logging.error(f"停止摄像头 {camera_id} 时发生错误: {str(e)}")

            self.camera_threads.clear()
            logging.info("所有资源已清理")
        except Exception as e:
            logging.error(f"清理资源时发生错误: {str(e)}")

    def check_api_status(self):
        """
        检查API服务器状态

        Returns:
            bool: True表示API服务正常运行，False表示服务异常
        """
        try:
            # 使用 get_cameras_config 方法获取摄像头配置
            cameras_config = self.get_cameras_config()
            # 如果能获取到有效的摄像头置，则认为API正常行
            return bool(cameras_config)
        except Exception as e:
            logging.error(f"检查API状态时发生错误: {str(e)}")
            return False

    def check_camera_health(self, camera_id: str) -> bool:
        """
        检查摄像头健康状态

        Args:
            camera_id: 摄像头ID

        Returns:
            bool: True表示摄像头正常工作，False表示摄像头异常

        检查项目:
            1. 摄像头是否在注册列表中
            2. 摄像头对象是否有效
            3. 摄像头是否能正常读取帧
        """
        if camera_id not in self.camera_captures:
            return False

        cap = self.camera_captures[camera_id]
        if not cap or not cap.isOpened():
            return False

        ret, _ = cap.read()
        return ret

    def get_cameras_config(self) -> List[Dict]:
        """获取摄像头配置列表

        Returns:
            List[Dict]: 摄像头配置列表
        """
        try:
            # 使用api1_test.py中的方式获取设备列表
            result = get_device_list(main_type="cam")
            if not result:
                logging.error("获取设备列表失败")
                return []

            # 处理设备数据并构建配置
            camera_configs = get_all_data_by_process_device_data(result)
            logging.info(f"成功获取 {len(camera_configs)} 个摄像头配置")
            return camera_configs

        except Exception as e:
            logging.error(f"获取摄像头配置时发生错误: {str(e)}")
            return []

    def run(self):
        """
        运行监控系统的主循环

        主要功能:
            1. 定时执行数据清理(凌晨2点)
            2. 检查API服务器状态
            3. 管理摄像头的启动和停止
            4. 错误处理和系统恢复
        """
        consecutive_errors = 0
        max_consecutive_errors = 5
        current_configs = {}  # 存储当前的配置

        while self.system_running:
            try:
                # 每天凌晨2点执行清理操作
                current_time = datetime.now()
                if current_time.hour == 2 and current_time.minute == 0:
                    self.cleanup_manager.cleanup_old_images()

                # 查API服务器状态并获取摄像头配置
                cameras_config = self.get_cameras_config()
                self.is_api_running = bool(cameras_config)

                if self.is_api_running:
                    success_msg = f"成功获取 {len(cameras_config)} 个摄像头配置"
                    print(f"{time.strftime('%Y-%m-%d %H:%M:%S')} {success_msg}")
                    logging.info(success_msg)

                    # 将配置转换为字典，方便比较
                    new_configs = {
                        cfg['camera_id']: cfg
                        for cfg in cameras_config
                    }

                    # 处理每个摄像头
                    for camera_id, new_config in new_configs.items():
                        try:
                            old_config = current_configs.get(camera_id)

                            # 如果是新摄像头或配置有变化，需要重启线程
                            if old_config != new_config:
                                if camera_id in self.camera_threads:
                                    stop_msg = f"检测到摄像头 {camera_id} 配置变化，正在重启..."
                                    print(
                                        f"{time.strftime('%Y-%m-%d %H:%M:%S')} {stop_msg}")
                                    logging.info(stop_msg)
                                    self.stop_camera_monitoring(camera_id)

                                start_msg = f"启动摄像头 {camera_id} 的监控"
                                print(
                                    f"{time.strftime('%Y-%m-%d %H:%M:%S')} {start_msg}")
                                logging.info(start_msg)
                                self.start_camera_monitoring(new_config)

                        except Exception as e:
                            error_msg = f"处理摄像头 {camera_id} 时发生错误: {str(e)}"
                            print(
                                f"{time.strftime('%Y-%m-%d %H:%M:%S')} {error_msg}")
                            logging.error(error_msg)

                    # 停止已移除的摄像头
                    for camera_id in list(self.camera_threads.keys()):
                        if camera_id not in new_configs:
                            stop_msg = f"停止摄像头 {camera_id} 的监控"
                            print(
                                f"{time.strftime('%Y-%m-%d %H:%M:%S')} {stop_msg}")
                            logging.info(stop_msg)
                            self.stop_camera_monitoring(camera_id)

                    # 更新当前配置
                    current_configs = new_configs

                else:
                    wait_msg = "等待API服务器响应..."
                    print(f"{time.strftime('%Y-%m-%d %H:%M:%S')} {wait_msg}")
                    logging.info(wait_msg)

                # 系统待机
                time.sleep(self.check_interval)

                consecutive_errors = 0  # 重置错误计数
            except Exception as e:
                consecutive_errors += 1
                logging.error(
                    f"运行时发生错误 ({consecutive_errors}/{max_consecutive_errors}): {str(e)}")

                if consecutive_errors >= max_consecutive_errors:
                    logging.critical("连续错误次数过多，系统将重启...")
                    self.safe_cleanup_resources()
                    time.sleep(30)  # 等待系统稳定
                    consecutive_errors = 0

    def graceful_shutdown(self):
        """
        优雅关闭系统

        执行操作:
            1. 设置系统停止标志
            2. 清理所有资源
            3. 记录关闭日志
        """
        logging.info("正在关闭系统...")
        self.system_running = False
        self.safe_cleanup_resources()
        logging.info("系统已安全关闭")


def opshub_start():
    try:
        # 加载环境变量,验证环境变量是否存在
        EnvManager.load_env()

        # 设置日志
        setup_logging()

        # 创建并运行监控系统
        monitoring_system = VideoMonitoringSystem()
        monitoring_system.run()
    except KeyboardInterrupt:
        logging.info("收到终止信号，正在安全关闭系统...")
        if 'monitoring_system' in locals():
            monitoring_system.graceful_shutdown()
    except Exception as e:
        logging.error(f"系统发生严重错误: {str(e)}")
        if 'monitoring_system' in locals():
            monitoring_system.graceful_shutdown()


if __name__ == "__main__":
    start()
