-- 数据库索引优化脚本
-- 用于优化月报和场景月报查询性能

-- ========================================
-- 设备月报表索引优化
-- ========================================

-- 删除可能重复的索引（如果存在）
DROP INDEX IF EXISTS idx_device_monthly_report_year_month_camera;
DROP INDEX IF EXISTS idx_device_monthly_report_camera_year_month;

-- 创建复合索引：年月+设备ID（优化按年月查询所有设备的场景）
CREATE INDEX IF NOT EXISTS idx_device_monthly_report_year_month_camera 
ON device_monthly_report (report_year, report_month, camera_id);

-- 创建复合索引：设备ID+年月（优化按设备查询特定年月的场景）
CREATE INDEX IF NOT EXISTS idx_device_monthly_report_camera_year_month 
ON device_monthly_report (camera_id, report_year, report_month);

-- 创建报告名称的GIN索引（优化模糊查询性能）
DROP INDEX IF EXISTS idx_device_monthly_report_report_name_gin;
CREATE INDEX IF NOT EXISTS idx_device_monthly_report_report_name_gin 
ON device_monthly_report USING gin(report_name gin_trgm_ops);

-- ========================================
-- 场景月报表索引优化
-- ========================================

-- 删除可能重复的索引（如果存在）
DROP INDEX IF EXISTS idx_scene_monthly_report_year_month;
DROP INDEX IF EXISTS idx_scene_monthly_report_year_month_scene;
DROP INDEX IF EXISTS idx_scene_monthly_report_scene_year_month;

-- 创建基础年月索引
CREATE INDEX IF NOT EXISTS idx_scene_monthly_report_year_month 
ON scene_monthly_report (report_year, report_month);

-- 创建复合索引：年月+场景ID（优化按年月查询所有场景的场景）
CREATE INDEX IF NOT EXISTS idx_scene_monthly_report_year_month_scene 
ON scene_monthly_report (report_year, report_month, scene_id);

-- 创建复合索引：场景ID+年月（优化按场景查询特定年月的场景）
CREATE INDEX IF NOT EXISTS idx_scene_monthly_report_scene_year_month 
ON scene_monthly_report (scene_id, report_year, report_month);

-- 创建场景名称索引（优化场景名称模糊查询）
CREATE INDEX IF NOT EXISTS idx_scene_monthly_report_scene_name 
ON scene_monthly_report (scene_name);

-- 创建报告名称的GIN索引（优化模糊查询性能）
DROP INDEX IF EXISTS idx_scene_monthly_report_report_name_gin;
CREATE INDEX IF NOT EXISTS idx_scene_monthly_report_report_name_gin 
ON scene_monthly_report USING gin(report_name gin_trgm_ops);

-- ========================================
-- 其他表的索引优化
-- ========================================

-- frame_analysis_latest表索引优化（用于实时数据查询）
DROP INDEX IF EXISTS idx_frame_analysis_latest_timestamp_camera;
CREATE INDEX IF NOT EXISTS idx_frame_analysis_latest_timestamp_camera 
ON frame_analysis_latest (timestamp DESC, camera_id);

-- 创建覆盖索引（包含常用查询字段）
DROP INDEX IF EXISTS idx_frame_analysis_latest_coverage;
CREATE INDEX IF NOT EXISTS idx_frame_analysis_latest_coverage 
ON frame_analysis_latest (camera_id, timestamp DESC) 
INCLUDE (coverage_level, failure_reasons_number, alarm_status);

-- ========================================
-- 启用pg_trgm扩展（用于模糊查询优化）
-- ========================================

-- 创建pg_trgm扩展（如果不存在）
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- ========================================
-- 索引使用情况分析查询
-- ========================================

-- 查看索引使用统计
-- SELECT 
--     schemaname,
--     tablename,
--     indexname,
--     idx_scan as index_scans,
--     idx_tup_read as tuples_read,
--     idx_tup_fetch as tuples_fetched
-- FROM pg_stat_user_indexes 
-- WHERE tablename IN ('device_monthly_report', 'scene_monthly_report', 'frame_analysis_latest')
-- ORDER BY idx_scan DESC;

-- 查看表大小和索引大小
-- SELECT 
--     tablename,
--     pg_size_pretty(pg_total_relation_size(tablename::regclass)) as total_size,
--     pg_size_pretty(pg_relation_size(tablename::regclass)) as table_size,
--     pg_size_pretty(pg_total_relation_size(tablename::regclass) - pg_relation_size(tablename::regclass)) as index_size
-- FROM pg_tables 
-- WHERE tablename IN ('device_monthly_report', 'scene_monthly_report', 'frame_analysis_latest');

-- ========================================
-- 性能优化建议
-- ========================================

-- 1. 定期执行VACUUM ANALYZE以更新统计信息
-- VACUUM ANALYZE device_monthly_report;
-- VACUUM ANALYZE scene_monthly_report;
-- VACUUM ANALYZE frame_analysis_latest;

-- 2. 监控慢查询日志
-- 在postgresql.conf中设置：
-- log_min_duration_statement = 1000  # 记录执行时间超过1秒的查询

-- 3. 考虑分区表（如果数据量很大）
-- 可以按年份或年月对月报表进行分区

-- ========================================
-- 索引维护脚本
-- ========================================

-- 重建索引（在维护窗口期间执行）
-- REINDEX INDEX CONCURRENTLY idx_device_monthly_report_year_month_camera;
-- REINDEX INDEX CONCURRENTLY idx_scene_monthly_report_year_month_scene;

-- 查找未使用的索引
-- SELECT 
--     schemaname,
--     tablename,
--     indexname,
--     idx_scan
-- FROM pg_stat_user_indexes 
-- WHERE idx_scan = 0 
-- AND tablename IN ('device_monthly_report', 'scene_monthly_report');
