version: '3.8'

services:
  qwen2.5-vl:
    image: qwenllm/qwenvl:2.5-cu121
    container_name: qwen2.5
    # command: bash -c "python -m vllm.entrypoints.openai.api_server --served-model-name qwen2.5-vl:7b --model /app/model --port 8001 --trust-remote-code --disable-log-requests"

    command: bash -c "python -m vllm.entrypoints.openai.api_server --served-model-name qwen2.5-vl:7b --model /app/model --port 8001 --host 0.0.0.0 --limit-mm-per-prompt image=3 --max-model-len 8192 --gpu_memory_utilization 0.8 "
    deploy:
      resources:
        reservations:
          devices:
            - capabilities: [gpu]
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
      - NVIDIA_DRIVER_CAPABILITIES=compute,utility
      - HF_ENDPOINT=
      - TRANSFORMERS_OFFLINE=1
      - HF_HUB_OFFLINE=1
    ipc: host
    # 移除 network_mode: host
    ports:
      - "8001:8001"  # 将容器内的8000端口映射到主机的8001端口
    volumes:
      - ./Qwen/Qwen2.5-VL-7B-Instruct-AWQ/:/app/model/
    restart: unless-stopped

    networks:
      - my-network

networks:
  my-network:
    name: my-network  # 显式命名网络（便于跨项目通信）
    driver: bridge
    attachable: true  # 允许其他容器后期加入