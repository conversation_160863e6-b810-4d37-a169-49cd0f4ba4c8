import logging
import re
import cv2
import time
from pathlib import Path
from typing import Dict, List, Tuple, Any

from server.utils.handlers.base_handler import BaseFrameHandler
from llms.llm_api_server import llm_process_image_filter
from llms.yolo_api_server_filter import DeviceDetector as FilterDetector


class FilterHandler(BaseFrameHandler):
    """滤池处理器类

    负责处理滤池相关的视频帧分析，使用YOLO检测器进行故障检测
    """

    def __init__(self, processor):
        """初始化滤池处理器

        Args:
            processor (VideoFrameProcessor): 主处理器实例
        """
        super().__init__(processor)

        # 初始化滤池YOLO检测器
        config = getattr(processor, 'config', {})
        model_path = config.get('yolo_model_filter', 'llms/models/yolo/best-filter.pt')
        self.filter_detector = FilterDetector(model_path=model_path)

        # 获取保存配置
        yolo_save_result = config.get('yolo_save_result', {})
        self.save_filter_result = yolo_save_result.get('filter', False)  # 默认不保存滤池检测结果

        logging.info(f"滤池处理器初始化完成，YOLO模型路径: {model_path}, 保存检测结果: {self.save_filter_result}")
    
    def process_frame(self, frame, frame_count, save_dir, camera_id, sensor_data,
                      threshold, system_type, standard_image_path=None, current_time=None) -> Tuple:
        """处理滤池相关的视频帧，使用YOLO检测器进行故障检测

        Args:
            frame: 视频帧图像数据
            frame_count: 帧计数
            save_dir: 保存目录
            camera_id: 摄像头ID
            sensor_data: 传感器数据
            threshold: 覆盖率阈值
            system_type: 系统类型
            standard_image_path: 标准图片路径
            current_time: 当前时间

        Returns:
            tuple: (覆盖率, 分析结果, 警报状态, 是否异常, 图片路径, 分析建议, 故障类型列表)
        """
        logging.info(f"滤池处理器开始处理帧 - 摄像头ID: {camera_id}, 帧计数: {frame_count}, 系统类型: {system_type}")

        # 先准备帧但不保存，用于分析
        resized_frame, potential_frame_path = self._prepare_frame_without_save(frame, save_dir, camera_id, current_time)
        failure_reasons_type = []  # 出现故障的类型
        frame_path = ''  # 默认不保存图片

        # 使用YOLO检测器进行滤池故障检测
        if system_type in ['system_prompt_filter1', 'system_prompt_filter_multiple']:
            # 调用滤池YOLO检测器
            yolo_results = self.filter_detector.detect_devices(resized_frame, save_result=False)  # 先不保存YOLO结果
            logging.info(f"---------------------------")
            logging.info(f"滤池YOLO识别结果: {yolo_results}")
            logging.info(f"---------------------------")

            # 解析YOLO检测结果
            devices = yolo_results.get('devices', [])
            has_malfunction = any(device.get('status') == 'malfunction' for device in devices)

            # 根据检测结果生成分析结果
            if has_malfunction:
                # 检测到故障，准备保存带框线的图片
                coverage_float = 99
                failure_reasons_type.append('曝气头脱落或损坏')

                # 生成分析结果
                response_dict = {
                    "你的思考": "根据YOLO检测结果，识别的图片中存在局部凸起水体，符合曝气头脱落或损坏的判定标准。",
                    "是否反冲洗": "是",
                    "曝气头是否脱落或损坏": "是",
                    "调整建议": "1. 建议暂停滤池反冲洗流程，检查凸起区域对应的曝气头；\n2. 更换脱落或破损的曝气头，并确保重新固定牢固；\n3. 重启滤池，观察水面翻滚是否均匀。"
                }

                # 保存带框线的检测结果图片
                annotated_frame_path = self._save_annotated_frame(resized_frame, yolo_results, potential_frame_path, devices)
                if annotated_frame_path:
                    frame_path = annotated_frame_path
                    logging.info(f"检测到故障，已保存带框线标注的图片: {frame_path}")
                else:
                    # 如果标注保存失败，保存原始图片
                    save_success = self._save_frame(resized_frame, potential_frame_path)
                    if save_success:
                        frame_path = str(potential_frame_path)
                        logging.info(f"检测到故障，已保存原始图片: {frame_path}")
                    else:
                        logging.error(f"故障情况下保存图片失败")

            else:
                # 未检测到故障
                coverage_float = 1
                response_dict = {
                    "你的思考": "根据YOLO检测结果，识别的监控中没有曝气头出现故障",
                    "是否反冲洗": "否",
                    "曝气头是否脱落或损坏": "否",
                    "调整建议": "无需调整"
                }
                logging.info(f"滤池检测结果正常，未保存图片")
        else:
            # 对于非滤池系统类型，使用原有的处理逻辑
            response_dict = self._process_image_comparison(
                resized_frame, potential_frame_path, standard_image_path, system_type
            )

            value = response_dict.get('曝气头是否脱落或损坏', '否')
            if value == '是':
                coverage_float = 99
                failure_reasons_type.append('曝气头可能出现脱落或损坏')
                save_success = self._save_frame(resized_frame, potential_frame_path)
                if save_success:
                    frame_path = str(potential_frame_path)
                    logging.info(f"检测到异常，已保存图片: {frame_path}")
            else:
                coverage_float = 1
                logging.info(f"检测结果正常，未保存图片")

        # 生成分析建议
        all_situation_analysis = response_dict.get('你的思考', '')
        analysis_result = llm_process_image_filter(filter_result=all_situation_analysis, system_type=system_type)

        # 确定警报状态
        alarm_status_flag, is_abnormal = self._determine_alarm_status(coverage_float, threshold)

        logging.info(f"滤池处理器处理完成 - 摄像头ID: {camera_id}, 覆盖率: {coverage_float}, 警报状态: {alarm_status_flag}, 图片路径: {frame_path}")
        if failure_reasons_type:
            logging.warning(f"检测到故障类型: {', '.join(failure_reasons_type)}")

        return (coverage_float, all_situation_analysis, alarm_status_flag, is_abnormal,
                frame_path, analysis_result, failure_reasons_type)

    def _save_annotated_frame(self, frame, yolo_results, frame_path, devices):
        """保存带有YOLO检测框标注的图片

        Args:
            frame: 原始图像帧
            yolo_results: YOLO检测结果
            frame_path: 原始保存路径
            devices: 检测到的设备列表

        Returns:
            str: 保存的标注图片路径，失败时返回None
        """
        try:
            # 创建标注图片的保存路径
            frame_path_obj = Path(frame_path)
            annotated_filename = f"{frame_path_obj.stem}_annotated{frame_path_obj.suffix}"
            annotated_path = frame_path_obj.parent / annotated_filename

            # 复制原始图像用于标注
            annotated_frame = frame.copy()

            # 在图像上绘制检测框和标签
            for device in devices:
                bbox = device.get("bbox", [])
                status = device.get("status", "unknown")
                confidence = device.get("confidence", 0.0)
                device_id = device.get("id", 0)

                if len(bbox) >= 4:
                    x1, y1, x2, y2 = map(int, bbox)

                    # 根据状态选择颜色
                    if status == 'malfunction':
                        color = (0, 0, 255)  # 红色表示故障
                        label = f"故障 ID:{device_id} ({confidence:.2f})"
                    else:
                        color = (0, 255, 0)  # 绿色表示正常
                        label = f"正常 ID:{device_id} ({confidence:.2f})"

                    # 绘制检测框
                    cv2.rectangle(annotated_frame, (x1, y1), (x2, y2), color, 3)

                    # 绘制标签背景
                    label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.8, 2)[0]
                    cv2.rectangle(annotated_frame, (x1, y1 - label_size[1] - 10),
                                (x1 + label_size[0], y1), color, -1)

                    # 绘制标签文字
                    cv2.putText(annotated_frame, label, (x1, y1 - 5),
                              cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)

            # 添加时间戳和检测信息
            timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
            info_text = f"检测时间: {timestamp} | 故障数量: {len([d for d in devices if d.get('status') == 'malfunction'])}"
            cv2.putText(annotated_frame, info_text, (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

            # 保存标注后的图片
            success = cv2.imwrite(str(annotated_path), annotated_frame)
            if success:
                logging.info(f"成功保存带框线标注的图片: {annotated_path}")
                return str(annotated_path)
            else:
                logging.error(f"保存标注图片失败: {annotated_path}")
                return None

        except Exception as e:
            logging.error(f"保存标注图片时发生错误: {str(e)}", exc_info=True)
            return None