#!/usr/bin/env python3
"""
简单测试滤池处理逻辑
验证重构后的代码结构和逻辑是否正确
"""

import logging
import sys
import os
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_filter_handler_structure():
    """测试滤池处理器的代码结构"""
    
    try:
        # 测试导入
        logging.info("测试模块导入...")
        
        # 检查文件是否存在
        filter_handler_path = Path("server/utils/handlers/filter_handler.py")
        if not filter_handler_path.exists():
            logging.error("❌ filter_handler.py 文件不存在")
            return False
        
        # 读取文件内容并检查关键功能
        with open(filter_handler_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键功能是否存在
        checks = [
            ("FilterDetector导入", "from llms.yolo_api_server_filter import DeviceDetector as FilterDetector"),
            ("YOLO检测逻辑", "self.filter_detector.detect_devices"),
            ("条件保存逻辑", "has_malfunction"),
            ("标注图片保存方法", "_save_annotated_frame"),
            ("框线绘制", "cv2.rectangle"),
            ("故障标签", "故障 ID:"),
            ("时间戳添加", "检测时间:")
        ]
        
        all_passed = True
        for check_name, check_pattern in checks:
            if check_pattern in content:
                logging.info(f"✅ {check_name}: 存在")
            else:
                logging.error(f"❌ {check_name}: 缺失")
                all_passed = False
        
        # 检查video_processor.py的清理情况
        logging.info("\n检查video_processor.py的清理情况...")
        video_processor_path = Path("server/utils/video_processor.py")
        
        if video_processor_path.exists():
            with open(video_processor_path, 'r', encoding='utf-8') as f:
                video_content = f.read()
            
            # 检查是否已移除滤池YOLO处理代码
            removed_checks = [
                ("滤池YOLO处理逻辑已移除", "results_path = self.filter_detector.detect_devices"),
                ("滤池检测器导入已移除", "from llms.yolo_api_server_filter import detector")
            ]
            
            for check_name, check_pattern in removed_checks:
                if check_pattern not in video_content:
                    logging.info(f"✅ {check_name}")
                else:
                    logging.warning(f"⚠️ {check_name}: 仍然存在")
            
            # 检查配置保存是否存在
            if "self.config = config" in video_content:
                logging.info("✅ 配置保存逻辑: 存在")
            else:
                logging.error("❌ 配置保存逻辑: 缺失")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        logging.error(f"测试过程中发生错误: {str(e)}", exc_info=True)
        return False

def test_logic_flow():
    """测试逻辑流程"""
    
    logging.info("\n测试逻辑流程...")
    
    # 模拟YOLO检测结果
    test_cases = [
        {
            "name": "检测到故障",
            "yolo_result": {
                "devices": [
                    {"id": 0, "status": "malfunction", "confidence": 0.85, "bbox": [100, 100, 200, 200]}
                ]
            },
            "expected_save": True,
            "expected_coverage": 99
        },
        {
            "name": "未检测到故障",
            "yolo_result": {
                "devices": [
                    {"id": 0, "status": "normal", "confidence": 0.90, "bbox": [100, 100, 200, 200]}
                ]
            },
            "expected_save": False,
            "expected_coverage": 1
        },
        {
            "name": "无检测结果",
            "yolo_result": {
                "devices": []
            },
            "expected_save": False,
            "expected_coverage": 1
        }
    ]
    
    for test_case in test_cases:
        logging.info(f"测试用例: {test_case['name']}")
        
        devices = test_case["yolo_result"]["devices"]
        has_malfunction = any(device.get('status') == 'malfunction' for device in devices)
        
        if has_malfunction:
            coverage_float = 99
            should_save = True
            logging.info(f"  检测到故障: {should_save}, 覆盖率: {coverage_float}")
        else:
            coverage_float = 1
            should_save = False
            logging.info(f"  未检测到故障: {should_save}, 覆盖率: {coverage_float}")
        
        # 验证结果
        if should_save == test_case["expected_save"] and coverage_float == test_case["expected_coverage"]:
            logging.info(f"  ✅ 逻辑正确")
        else:
            logging.error(f"  ❌ 逻辑错误")
            return False
    
    return True

def main():
    """主测试函数"""
    
    logging.info("开始滤池处理器重构验证...")
    
    # 测试代码结构
    structure_ok = test_filter_handler_structure()
    
    # 测试逻辑流程
    logic_ok = test_logic_flow()
    
    # 总结
    logging.info("\n" + "=" * 50)
    if structure_ok and logic_ok:
        logging.info("✅ 所有测试通过！重构成功完成")
        logging.info("✅ 功能特性:")
        logging.info("  - 条件保存：只有检测到故障时才保存图片")
        logging.info("  - 框线标注：保存带有检测框和标签的图片")
        logging.info("  - 故障信息：提供详细的故障类型和位置")
        logging.info("  - 代码模块化：滤池逻辑集中在FilterHandler中")
        return True
    else:
        logging.error("❌ 部分测试失败，需要检查代码")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 滤池处理器重构验证通过！")
    else:
        print("\n💥 滤池处理器重构验证失败！")
        sys.exit(1)
