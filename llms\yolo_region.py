# ============================================== #
#                      画多个区域-耙斗                     #
# ============================================== #
import cv2
import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import atexit
from ultralytics import solutions
import time
import datetime
import logging
# from llms.utils.logger import setup_logging
from llms.utils.transform_processor import process_image

# 添加环境变量设置，避免 GUI 相关错误
os.environ["QT_QPA_PLATFORM"] = "offscreen"
# 避免 OpenCV 窗口相关的错误
os.environ["OPENCV_IO_ENABLE_OPENEXR"] = "1"

# 初始化日志
# setup_logging()
logger = logging.getLogger(__name__)

# 确保在程序退出时释放所有OpenCV资源
atexit.register(cv2.destroyAllWindows)

class YoloRegionCounter:
    def __init__(self, video_path=None, image_path=None, region_points=None, model_path="llms/models/yolo/best-dipper.pt", show=False, auto_reset_interval=None):
        """
        初始化YoloRegionCounter类
        
        参数:
            video_path: RTSP或本地视频文件路径
            image_path: 本地图像文件路径
            region_points: 检测区域字典，格式为 {"region-name": [(x1,y1), (x2,y2), ...]}
            model_path: YOLO模型路径
            show: 是否显示处理后的视频帧，默认为False（无界面环境下建议设为False）
            auto_reset_interval: 自动重置计数的时间间隔（秒），None表示不自动重置
        """
        self.video_path = video_path
        self.image_path = image_path
        
        # 默认区域设置
        if region_points is None:
            self.region_points = {
                "region-01": [(60, 550), (600, 550), (600, 300), (60, 300)],
                "region-02": [(600, 550), (1200, 550), (1200, 300), (600, 300)],
                "region-03": [(1200, 550), (1800, 550), (1800, 300), (1200, 300)],
            }
        else:
            self.region_points = region_points
            
        self.model_path = model_path
        self.show = show
        self.regioncounter = None
        
        # 计数重置相关
        self.auto_reset_interval = auto_reset_interval
        self.last_reset_time = time.time()
        
        # 预加载模型 - 在初始化时就加载模型，避免每次推理都重新加载
        self._init_regioncounter()

    def _init_regioncounter(self):
        """初始化区域计数器，预加载模型"""
        try:
            # 先确保关闭之前可能存在的计数器
            self._close_regioncounter()
            
            # 初始化区域计数器并加载模型
            self.regioncounter = solutions.RegionCounter(
                show=self.show,
                region=self.region_points,
                model=self.model_path,
            )
            logger.info("模型已预加载完成")
        except Exception as e:
            logger.error(f"初始化区域计数器失败: {e}")
            self.regioncounter = None
            raise
        
    def process_video(self, save_output=False, output_path=None):
        """
        处理视频并返回检测结果
        
        参数:
            save_output: 是否保存输出视频
            output_path: 输出视频保存路径
            
        返回:
            所有帧的检测结果列表
        """
        # 打开视频
        cap = cv2.VideoCapture(self.video_path)
        assert cap.isOpened(), "Error reading video file"
        
        # 视频参数
        w, h, fps = (int(cap.get(x)) for x in (cv2.CAP_PROP_FRAME_WIDTH, cv2.CAP_PROP_FRAME_HEIGHT, cv2.CAP_PROP_FPS))
        
        # 视频写入器
        video_writer = None
        if save_output:
            # 使用时间戳命名文件
            if output_path is None:
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path = f"tests/region_counting_{timestamp}.mp4"
            video_writer = cv2.VideoWriter(output_path, cv2.VideoWriter_fourcc(*"mp4v"), fps, (w, h))
        
        # 确保区域计数器已初始化
        if self.regioncounter is None:
            self._init_regioncounter()
        
        all_results = []
        
        # 处理视频
        while cap.isOpened():
            success, im0 = cap.read()
            
            if not success:
                logger.info("视频帧为空或处理完成。")
                break
            
            results = self.regioncounter(im0)
            all_results.append(results)
            
            logger.debug('识别结果 %s', results)
            
            if save_output and video_writer is not None:
                video_writer.write(results.plot_im)
        
        # 释放资源
        cap.release()
        if video_writer is not None:
            video_writer.release()
        cv2.destroyAllWindows()
        
        return all_results
    
    def process_image(self, image_path=None, save_output=False, output_path=None):
        """
        处理单个图像文件
        
        参数:
            image_path: 图像文件路径，如不提供则使用初始化时的路径
            save_output: 是否保存输出图像
            output_path: 输出图像保存路径
            
        返回:
            图像的检测结果
        """
        # 使用传入的路径或实例化时设置的路径
        img_path = image_path if image_path else self.image_path
        assert img_path is not None, "必须提供图像路径"
        
        # 读取图像
        # im0 = process_image(img_path,params_file='llms/utils/split_coords/pre_line.txt',output_path ='tests')
        im0 = process_image(img_path,params_file='llms/utils/split_coords/pre_line.txt')

        assert im0 is not None, f"无法读取图像: {img_path}"
        
        # 确保区域计数器已初始化
        if self.regioncounter is None:
            self._init_regioncounter()
            
        try:
            # 处理图像
            results = self.regioncounter(im0)
            
            # 保存结果
            if save_output:
                # 使用时间戳命名文件
                if output_path is None:
                    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                    output_path = f"tests/region_counting_{timestamp}.jpg"
                cv2.imwrite(output_path, results.plot_im)
            
            # 显示结果 - 仅在show=True且环境支持显示时
            if self.show:
                try:
                    cv2.imshow("Region Counting Result", results.plot_im)
                    # 使用较短的等待时间，避免卡住
                    cv2.waitKey(1000)
                except Exception as e:
                    logger.warning(f"无法显示图像，可能是在无界面环境中运行。错误: {e}")
            
            return results
        except Exception as e:
            logger.error(f"处理图像时出错: {e}")
            # 如果处理过程中出错，尝试重新初始化计数器
            self._init_regioncounter()
            raise
            
    def _close_regioncounter(self):
        """关闭区域计数器并释放资源"""
        try:
            if self.regioncounter is not None:
                if hasattr(self.regioncounter, 'close') and callable(self.regioncounter.close):
                    self.regioncounter.close()
                # 确保计数器被标记为关闭
                self.regioncounter = None
        except Exception as e:
            logger.error(f"关闭区域计数器时出错: {e}")
    
    def _close_windows(self):
        """关闭所有OpenCV窗口"""
        try:
            # 更安全的方式：直接尝试销毁所有窗口，如果没有窗口会静默失败
            cv2.destroyAllWindows()
            # 多次调用以确保窗口关闭（这是一个已知的OpenCV问题的解决方法）
            for i in range(5):
                cv2.waitKey(1)
        except cv2.error:
            # OpenCV错误，通常是没有窗口存在，直接返回
            return
        except Exception as e:
            # 降低日志级别，避免在正常关闭时产生错误日志
            logger.debug(f"关闭窗口时出错 (这通常是正常的): {e}")
    
    def __del__(self):
        """析构函数，确保资源在对象销毁时被释放"""
        try:
            self._close_regioncounter()
            self._close_windows()
        except Exception as e:
            # 在析构函数中不应该抛出异常，只记录日志
            logger.debug(f"析构函数中清理资源时出错 (这通常是正常的): {e}")
    
    def process_frame(self, frame, save_output=False, output_path=None):
        """
        处理单个图像帧
        
        参数:
            frame: 输入的图像帧
            save_output: 是否保存输出图像
            output_path: 输出图像保存路径
        返回:
            当前帧的检测结果
        """
        # 确保区域计数器已初始化
        if self.regioncounter is None:
            self._init_regioncounter()
            
        # 如果设置了自动重置间隔，检查是否需要重置计数
        if self.auto_reset_interval is not None:
            current_time = time.time()
            if current_time - self.last_reset_time >= self.auto_reset_interval:
                self.reset_counter()
                self.last_reset_time = current_time
        
        try:
            # 处理图像
            frame = process_image(frame,params_file='llms/utils/split_coords/pre_line.txt',)
            results = self.regioncounter(frame)
            # 是否保存结果
            if save_output:
                # 使用时间戳命名文件
                if output_path is None:
                    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                    output_path = f"tests/region_counting_{timestamp}.jpg"
                cv2.imwrite(output_path, results.plot_im)
            return results
        except Exception as e:
            logger.error(f"处理帧时出错: {e}")
            # 如果处理过程中出错，尝试重新初始化计数器
            self._init_regioncounter()
            raise
            
    def reset_counter(self):
        """
        重置所有区域的计数器
        """
        if self.regioncounter is not None:
            try:
                # 获取当前的region_counter对象
                if hasattr(self.regioncounter, 'region_counter'):
                    # 重置每个区域的计数
                    for region_name in self.region_points.keys():
                        if region_name in self.regioncounter.region_counter:
                            # 重置该区域的所有类别计数
                            for class_id in self.regioncounter.region_counter[region_name]:
                                self.regioncounter.region_counter[region_name][class_id] = 0
                    logger.info(f"所有区域计数已重置")
                else:
                    logger.warning("regioncounter对象没有region_counter属性，无法重置计数")
            except Exception as e:
                logger.error(f"重置计数器时出错: {e}")
                
    def reset_counter_by_region(self, region_name):
        """
        重置指定区域的计数器
        
        参数:
            region_name: 要重置的区域名称
        """
        if self.regioncounter is not None:
            try:
                # 获取当前的region_counter对象
                if hasattr(self.regioncounter, 'region_counter'):
                    # 检查区域名称是否存在
                    if region_name in self.regioncounter.region_counter:
                        # 重置该区域的所有类别计数
                        for class_id in self.regioncounter.region_counter[region_name]:
                            self.regioncounter.region_counter[region_name][class_id] = 0
                        logger.info(f"区域 '{region_name}' 的计数已重置")
                    else:
                        logger.warning(f"找不到区域 '{region_name}'")
                else:
                    logger.warning("regioncounter对象没有region_counter属性，无法重置计数")
            except Exception as e:
                logger.error(f"重置区域计数器时出错: {e}")
                
    def get_counter_stats(self):
        """
        获取所有区域的当前计数统计
        
        返回:
            区域计数统计字典
        """
        stats = {}
        if self.regioncounter is not None:
            try:
                # 获取当前的region_counter对象
                if hasattr(self.regioncounter, 'region_counter'):
                    stats = self.regioncounter.region_counter.copy()
                else:
                    logger.warning("regioncounter对象没有region_counter属性，无法获取计数统计")
            except Exception as e:
                logger.error(f"获取计数统计时出错: {e}")
        return stats


# 使用示例
if __name__ == "__main__":
    # 导入需要的模块
    import time
    region_points = {
        "region-01": [(160, 300), (600, 300), (600, 0), (160, 0)],  # 向右移动100像素
        "region-02": [(600, 300), (1100, 300), (1100, 0), (600, 0)],  # 向右移动100像素
        "region-03": [(1100, 300), (1500, 300), (1500, 0), (1100, 0)],  # 向右移动100像素
        }

    # 定义检测区域
    # region_points = {
    #     "region-01": [(60, 500), (500, 500), (500, 200), (60, 200)],
    #     "region-02": [(500, 500), (1000, 500), (1000, 200), (500, 200)],
    #     "region-03": [(1000, 500), (1500, 500), (1500, 200), (1000, 200)],
    # }

    # ===================== 图像识别示例 =====================
    logger.info("\n开始图像识别示例...")
    test_images = [
        # '/home/<USER>/llm_project/yolo_project/datasets/竹料/test/5.00.jpg',
        '/home/<USER>/llm_project/yolo_project/datasets/竹料/test/3.00.jpg',
        '/home/<USER>/llm_project/yolo_project/datasets/竹料/test/1.20.jpg',
        '/home/<USER>/llm_project/yolo_project/datasets/竹料/test/5.00.jpg',
        '/home/<USER>/llm_project/yolo_project/datasets/竹料/test/frame_4058_2025_01_15_13_10_55.jpg',
    ]
    # image_counter = YoloRegionCounter(
    #         # image_path=image_path,
    #         region_points=region_points,
    #         model_path="llms/models/yolo/best.pt",
    #         show=False  # 在无界面环境下设为False
    #     )
    for i, image_path in enumerate(test_images):
        logger.info(f"\n测试图像 {i+1}/{len(test_images)}: {image_path}")
    # image_path = "/home/<USER>/llm_project/yolo_project/datasets/竹料/test/5.00.jpg"
        # 创建YoloRegionCounter实例用于图像识别 初始化模型
        image_counter = YoloRegionCounter(
            # image_path=image_path,
            region_points=region_points,
            model_path="llms/models/yolo/best-dipper.pt",
            show=False  # 在无界面环境下设为False
        )

        try:
            # 处理图像并获取结果
            image_results = image_counter.process_image(image_path,save_output=True, output_path="tests/region_counting_result.jpg")
            logger.info(f'图像识别结果: {image_results}')
        finally:
        #     # 强制释放资源
        #     image_counter._close_regioncounter()
        #     image_counter._close_windows()
            logger.info("图像识别完成，资源已释放")
        
    # ===================== 视频处理示例 =====================
    # logger.info("\n开始视频处理示例...")
    # # 本地视频文件
    # video_path = "/home/<USER>/Downloads/3月25日.mp4"
    # # 或者使用RTSP
    # # video_path = "rtsp://admin:GZE9168168@**************:554/live0"
    
    # # 创建YoloRegionCounter实例用于视频处理
    # video_counter = YoloRegionCounter(
    #     video_path=video_path,
    #     region_points=region_points,
    #     model_path="llms/models/yolo/best-dipper.pt",
    #     show=False  # 在无界面环境下设为False
    # )
    
    # try:
    #     # 处理视频并获取结果
    #     video_results = video_counter.process_video(save_output=True, output_path="region_counting_video.mp4")
    #     logger.info('视频处理结果: %s', video_results)
    # except Exception as e:
    #     logger.error(f"视频处理出错: {e}")
    # finally:
    #     # 强制释放资源
    #     video_counter._close_regioncounter()
    #     video_counter._close_windows()
    #     logger.info("视频处理完成，资源已释放")
    
    # # 确保程序退出前完全释放资源
    # logger.info("\n所有处理完成，程序即将退出...")
    # import time
    # time.sleep(1)
    
    # 如果程序仍然没有退出，可以取消注释下面的行
    # sys.exit(0)
