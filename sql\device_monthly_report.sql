-- 创建设备月报表,
CREATE TABLE IF NOT EXISTS device_monthly_report (
    id SERIAL PRIMARY KEY,                            -- 主键ID
    camera_id VARCHAR(50) NOT NULL,                   -- 设备ID/摄像头ID
    device_name VARCHAR(100),                         -- 设备名称
    report_year INTEGER NOT NULL,                     -- 报告年份
    report_month INTEGER NOT NULL,                    -- 报告月份
    normal_count INTEGER DEFAULT 0,                   -- 正常记录数
    warning_count INTEGER DEFAULT 0,                  -- 告警记录数
    transition_count INTEGER DEFAULT 0,               -- 状态转变记录数
    normal_analysis TEXT,                             -- 正常运行分析
    warning_analysis TEXT,                            -- 告警分析
    transition_analysis TEXT,                         -- 状态转变分析
    full_report TEXT,                                 -- 完整报告文本
    summary_analysis TEXT,                            -- 全文报告总结（新增字段）
    raw_data JSONB,                                   -- 原始数据JSON
    main_failure_types JSONB,                         -- 主要故障类型统计JSON，格式: {"故障类型1": 次数, "故障类型2": 次数}
    failure_type_summary TEXT,                        -- 故障类型摘要文本，用于快速了解主要故障情况
    analysis_data JSONB,                              -- 分析数据JSON
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,   -- 创建时间
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,   -- 更新时间
    submitter VARCHAR(50) DEFAULT 'AI巡检员',         -- 提交人
    report_name VARCHAR(200) GENERATED ALWAYS AS (report_year::text || '年' || report_month::text || '月' || device_name || '巡检月报') STORED, -- 月报名称
    UNIQUE (camera_id, report_year, report_month)     -- 设备ID、年份和月份的唯一约束
);

-- 索引
CREATE INDEX IF NOT EXISTS idx_device_monthly_report_camera_id ON device_monthly_report (camera_id);
CREATE INDEX IF NOT EXISTS idx_device_monthly_report_year_month ON device_monthly_report (report_year, report_month);
CREATE INDEX IF NOT EXISTS idx_device_monthly_report_device_name ON device_monthly_report (device_name);
CREATE INDEX IF NOT EXISTS idx_device_monthly_report_report_name ON device_monthly_report (report_name); -- 添加月报名称索引用于加速模糊查询

-- 注释
COMMENT ON TABLE device_monthly_report IS '设备月报表，存储每个设备每月的运行分析报告';
COMMENT ON COLUMN device_monthly_report.id IS '主键ID';
COMMENT ON COLUMN device_monthly_report.camera_id IS '设备ID/摄像头ID';
COMMENT ON COLUMN device_monthly_report.device_name IS '设备名称';
COMMENT ON COLUMN device_monthly_report.report_year IS '报告年份';
COMMENT ON COLUMN device_monthly_report.report_month IS '报告月份';
COMMENT ON COLUMN device_monthly_report.normal_count IS '正常记录数';
COMMENT ON COLUMN device_monthly_report.warning_count IS '告警记录数';
COMMENT ON COLUMN device_monthly_report.transition_count IS '状态转变记录数';
COMMENT ON COLUMN device_monthly_report.normal_analysis IS '正常运行分析文本';
COMMENT ON COLUMN device_monthly_report.warning_analysis IS '告警分析文本';
COMMENT ON COLUMN device_monthly_report.transition_analysis IS '状态转变分析文本';
COMMENT ON COLUMN device_monthly_report.full_report IS '完整报告文本（大模型生成）';
COMMENT ON COLUMN device_monthly_report.summary_analysis IS '全文报告总结（对完整报告的总结分析）';
COMMENT ON COLUMN device_monthly_report.raw_data IS '原始数据JSON，包括原始的warning_data、non_warning_data和transition_data';
COMMENT ON COLUMN device_monthly_report.main_failure_types IS '主要故障类型统计JSON，格式: {"故障类型1": 次数, "故障类型2": 次数}';
COMMENT ON COLUMN device_monthly_report.failure_type_summary IS '故障类型摘要文本，用于快速了解主要故障情况';
COMMENT ON COLUMN device_monthly_report.created_at IS '创建时间';
COMMENT ON COLUMN device_monthly_report.updated_at IS '更新时间';
COMMENT ON COLUMN device_monthly_report.submitter IS '提交人';
COMMENT ON COLUMN device_monthly_report.report_name IS '月报名称，格式为"年+月+设备名称+巡检月报"';

-- 为已存在的表添加report_name列（如果表已经存在且需要更新）
ALTER TABLE device_monthly_report ADD COLUMN IF NOT EXISTS report_name VARCHAR(200) 
GENERATED ALWAYS AS (report_year::text || '年' || 
                    report_month::text || '月' || 
                    device_name || '巡检月报') STORED;

-- 为已存在的表添加submitter列（如果表已经存在且需要更新）
ALTER TABLE device_monthly_report ADD COLUMN IF NOT EXISTS submitter VARCHAR(50) DEFAULT 'AI巡检员';

-- 为已存在的表添加summary_analysis列（如果表已经存在且需要更新）
ALTER TABLE device_monthly_report ADD COLUMN IF NOT EXISTS summary_analysis TEXT;

-- 为已存在的表添加故障类型相关字段（如果表已经存在且需要更新）
ALTER TABLE device_monthly_report ADD COLUMN IF NOT EXISTS main_failure_types JSONB;
ALTER TABLE device_monthly_report ADD COLUMN IF NOT EXISTS failure_type_summary TEXT;

-- 为已存在的表添加analysis_data字段（如果表已经存在且需要更新）
ALTER TABLE device_monthly_report ADD COLUMN IF NOT EXISTS analysis_data JSONB;
