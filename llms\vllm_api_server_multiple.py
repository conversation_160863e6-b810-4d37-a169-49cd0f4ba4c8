"""
多张视频帧识别服务
用于处理多个图像帧的视觉语言模型(VLM)API服务器
多图的一般是好氧池识别
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import time
import logging
import cv2
import base64
import numpy as np
from llms.config import SYSTEM
from llms.config_filter import SYSTEM_FILTER
from llms.config_aerobic import SYSTEM_AEROBIC
from llms.config_bucket_dipper import SYSTEM_BUCKET_DIPPER
from llms.config_bucket_dipper_shaft import SYSTEM_BUCKET_DIPPER_SHAFT
from llms.config_secondary_clarifier import SYSTEM_SECONDARY_CLARIFIER
from openai import OpenAI
import re
from config_file import config

logger = logging.getLogger(__name__)

def frame_numpy_to_base64(frame: np.ndarray) -> str:
    """
    将numpy数组格式的图像转换为base64编码字符串
    
    Args:
        frame (np.ndarray): 输入的图像数组
        
    Returns:
        str: 包含data URI前缀的base64编码图像字符串
    """
    # 确保图像是RGB格式
    if len(frame.shape) == 2:  # 灰度图转RGB
        frame = cv2.cvtColor(frame, cv2.COLOR_GRAY2RGB)
    elif frame.shape[2] == 4:  # RGBA转RGB
        frame = cv2.cvtColor(frame, cv2.COLOR_RGBA2RGB)
    
    # 使用JPEG格式，并确保质量适中（不要太高也不要太低）
    _, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 90])
    base64_image = base64.b64encode(buffer).decode('utf-8')
    # 按照火山引擎API要求添加data URI前缀
    return f"data:image/jpeg;base64,{base64_image}"

def frame_img_to_base64(img_path: str) -> str:
    """
    将图片文件转换为base64编码字符串
    
    Args:
        img_path (str): 图片文件的路径
        
    Returns:
        str: 包含data URI前缀的base64编码图像字符串
    """
    # 检查文件是否存在
    if not os.path.exists(img_path):
        raise FileNotFoundError(f"图片文件不存在: {img_path}")
    
    try:
        # 根据文件扩展名确定MIME类型
        ext = os.path.splitext(img_path)[1].lower()
        mime_type = "image/jpeg"  # 默认MIME类型
        
        # 对于特定格式，保持原始格式
        if ext in [".png", ".gif", ".webp"]:
            if ext == ".png":
                mime_type = "image/png"
            elif ext == ".gif":
                mime_type = "image/gif"
            elif ext == ".webp":
                mime_type = "image/webp"
            
            # 直接读取文件并保持原始格式
            with open(img_path, 'rb') as img_file:
                img_base = base64.b64encode(img_file.read()).decode('utf-8')
                return f"data:{mime_type};base64,{img_base}"
        
        # 对于其他格式或JPEG，使用OpenCV处理并转换为JPEG
        img = cv2.imread(img_path)
        if img is None:
            raise ValueError(f"无法读取图像: {img_path}，可能是不支持的图片格式")
        
        # 确保图像是RGB格式
        if len(img.shape) == 2:  # 灰度图转RGB
            img = cv2.cvtColor(img, cv2.COLOR_GRAY2RGB)
        elif img.shape[2] == 4:  # RGBA转RGB
            img = cv2.cvtColor(img, cv2.COLOR_RGBA2RGB)
        
        # 使用JPEG格式编码
        _, buffer = cv2.imencode('.jpg', img, [cv2.IMWRITE_JPEG_QUALITY, 90])
        base64_image = base64.b64encode(buffer).decode('utf-8')
        # 按照火山引擎API要求添加data URI前缀
        return f"data:{mime_type};base64,{base64_image}"
    except Exception as e:
        logger.error(f"图像转换失败: {e}")
        raise

def process_image_multiple(
    frames: list, 
    system_type: str = SYSTEM['system_prompt_multiple'],
    max_retries: int = 3,
    retry_delay: int = 2,
    image_labels: list = None  # 添加图片标签参数
) -> dict:
    """
    处理多张图片并通过VLM模型进行分析
    
    Args:
        frames (list): 图片列表，可以包含numpy数组(图片)或图片路径
        system_type (str): 系统提示词类型，默认使用system_prompt_multiple
        max_retries (int): API调用最大重试次数，默认3次
        retry_delay (int): 重试间隔时间(秒)，默认2秒
        image_labels (list): 可选的图片标签列表，用于标识每张图片的角色，如["基准图", "对比图"]
    
    Returns:
        dict: 模型分析结果的字典
            成功: 包含分析结果的字典
            失败: 空字典 {}
            
    Raises:
        EnvironmentError: 当无法获取必要的环境变量时抛出
    """
    # 获取环境变量
    api_key = config.env_vars.get("QWEN2_VL_API_KEY")
    base_url = config.env_vars.get("QWEN2_VL_BASE_URL")
    model = config.env_vars.get("QWEN2_VL_MODEL")
    
    if not all([api_key, base_url, model]):
        raise EnvironmentError("无法从configs/.env获取必要的环境变量")
    
    logger.info(f"---------------------------")
    logger.info(f"多图提示词是：: {system_type}")
    logger.info(f"---------------------------")
    
    # 选择系统提示词
    if system_type =='system_prompt_aerobic_multiple1':
        system_prompt = SYSTEM_AEROBIC['system_prompt_aerobic_multiple1']
    elif system_type == 'system_prompt_aerobic_multiple2':
        system_prompt = SYSTEM_AEROBIC['system_prompt_aerobic_multiple2']
    elif system_type == 'system_prompt_bucket_dipper_multiple':
        system_prompt = SYSTEM_BUCKET_DIPPER['system_prompt_bucket_dipper_multiple']
    elif system_type == 'system_prompt_waste_percentage_multiple':
        system_prompt = SYSTEM_BUCKET_DIPPER_SHAFT['system_prompt_waste_percentage_multiple']
    elif system_type == 'system_prompt_bulky_waste_multiple':
        system_prompt = SYSTEM_BUCKET_DIPPER_SHAFT['system_prompt_bulky_waste_multiple']
    elif system_type == 'system_prompt_slat_damage_multiple':
        system_prompt = SYSTEM_BUCKET_DIPPER_SHAFT['system_prompt_slat_damage_multiple']
    # ========================== 滤池的 ========================= #
    elif system_type == 'system_prompt_filter_multiple':
        system_prompt = SYSTEM_FILTER['system_prompt_filter_multiple']
    elif system_type == 'system_prompt_color_comparison':
        system_prompt = SYSTEM_AEROBIC['system_prompt_color_comparison']
    # ======================== 前后水位对比 ======================== #
    elif system_type == 'system_prompt_water_level_comparison':
        system_prompt = SYSTEM_SECONDARY_CLARIFIER['system_prompt_water_level_comparison']
    else:
        logger.warning(f"未找到匹配的系统提示词类型: {system_type}，使用默认提示词")
        system_prompt = SYSTEM['system_prompt_multiple']
    
    # 如果未提供图片标签，则使用默认标签
    if not image_labels or len(image_labels) < len(frames):
        image_labels = [f"图片{i+1}" for i in range(len(frames))]
    
    # 将所有输入图片转换为base64格式
    base64_images = []
    for i, frame in enumerate(frames):
        try:
            if isinstance(frame, np.ndarray):
                logger.info(f"处理第{i+1}张图片 (numpy数组)")
                base64_image = frame_numpy_to_base64(frame)
            else:
                logger.info(f"处理第{i+1}张图片: {frame}")
                base64_image = frame_img_to_base64(frame)
            base64_images.append((base64_image, image_labels[i]))
        except Exception as e:
            logger.error(f"图片 {i+1} 处理失败: {e}")
            raise ValueError(f"图片处理失败: {e}")
   
    client = OpenAI(
        api_key=api_key,
        base_url=base_url
    )
    
    # 构建消息内容，保持图片顺序
    content = []
    
    # 首先添加所有图片，保持原始顺序
    for i, (base64_image, label) in enumerate(base64_images):
        try:
            content.append({
                'type': 'image_url',
                'image_url': {'url': base64_image}
            })
            # 在每张图片后添加标识文本
            content.append({
                'type': 'text',
                'text': f"【{label}】"
            })
            logger.info(f"已添加图片 {i+1}: {label}")
        except Exception as e:
            logger.error(f"添加图片 {i+1} 到内容时出错: {e}")
            raise ValueError(f"构建API请求失败: {e}")
    
    # 最后添加查询文本
    content.append({'type': 'text', 'text': "根据提示词分析上述图片并输出内容,注意第二张是需要识别和判断的图片。"})

    messages = [
        {'role': 'system', 'content': system_prompt},
        {'role': 'user', 'content': content}
    ]
    
    # 添加重试逻辑
    for attempt in range(max_retries):
        try:
            logger.info(f"尝试调用API (尝试 {attempt + 1}/{max_retries})")
            resp = client.chat.completions.create(
                model=model,
                messages=messages,
                seed=0,
                temperature=0.7
            )
            response_text = resp.choices[0].message.content
            logger.info(f"API响应成功，开始解析结果")
            logger.info(f"原始响应: {response_text}")
            
            # 尝试解析响应
            pattern = r'"(.*?)"\s*[:：]\s*"(.*?)"'
            matches = re.findall(pattern, response_text)
            response_dict = {key: value for key, value in matches}
            
            if response_dict:  # 如果成功解析到数据
                logger.info(f"成功解析结果，找到 {len(response_dict)} 个键值对")
                return response_dict
            else:
                logger.warning(f"API响应解析失败，未找到匹配的键值对")
                
        except Exception as e:
            error_msg = str(e)
            logger.error(f"API 调用或解析错误 (尝试 {attempt + 1}/{max_retries}): {error_msg}")
            
            # 检查是否是图像格式错误
            if "UnsupportedImageFormat" in error_msg:
                logger.error("图像格式不被支持，尝试转换图像格式后重试")
                # 如果是最后一次尝试，记录更详细的错误信息
                if attempt == max_retries - 1:
                    logger.error(f"详细错误信息: {error_msg}")
            
            if attempt < max_retries - 1:  # 如果不是最后一次尝试
                logger.info(f"等待 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
                continue
    
    logger.error("所有重试都失败了")
    return {}

if __name__ == "__main__":
    # 基准图像路径
    base_image = "/home/<USER>/llm_project/datansha/save_data/datasets/2025/05/27/robot_pool_4050_2025_05_27_16_56_15.jpg"
    # base_image = "/home/<USER>/llm_project/datasets/02/23/frame_4040_2025_02_23_01_38_41.jpg"
    # 第二个路径可以是文件或文件夹
    second_path = "/home/<USER>/llm_project/datansha/save_data/datasets/2025/05/27/robot_pool_4050_2025_05_27_16_54_30.jpg"
    # second_path = "/home/<USER>/llm_project/datasets/02/23/frame_4040_2025_02_23_01_38_41.jpg"
    
    # 检查图片路径是否存在
    if not os.path.exists(base_image):
        logger.error(f"基准图像不存在: {base_image}")
        # print(f"错误: 基准图像不存在: {base_image}")
        sys.exit(1)
    
    # 检查第二个路径是文件还是文件夹
    if os.path.isfile(second_path):
        # 如果是文件，直接处理这一组图像
        images = [base_image, second_path]
        # 为图片添加明确的标签
        image_labels = ["基准图像", "对比图像"]
        try:
            result = process_image_multiple(
                images, 
                system_type='system_prompt_water_level_comparison', # system_prompt_water_level_comparison
                image_labels=image_labels
            )
            print(f"处理结果: {result}")
        except Exception as e:
            logger.error(f"处理图像时出错: {e}")
            print(f"错误: {e}")
    else:
        # 如果是文件夹，遍历处理每个图像
        if not os.path.exists(second_path):
            logger.error(f"文件夹不存在: {second_path}")
            print(f"错误: 文件夹不存在: {second_path}")
            sys.exit(1)
            
        valid_extensions = ('.jpg', '.jpeg', '.png', '.bmp')
        folder_images = [
            os.path.join(second_path, f) 
            for f in os.listdir(second_path) 
            if f.lower().endswith(valid_extensions)
        ]
        
        if not folder_images:
            logger.warning(f"文件夹中没有找到支持的图像文件: {second_path}")
            print(f"警告: 文件夹中没有找到支持的图像文件: {second_path}")
            sys.exit(1)
        
        # 对文件夹中的每张图片单独处理
        for img_path in folder_images:
            print(f"\n处理图片: {img_path}")
            images = [base_image, img_path]
            # 为图片添加明确的标签
            image_labels = ["基准图像", "对比图像"]
            try:
                result = process_image_multiple(
                    images, 
                    system_type='system_prompt_water_level_comparison',
                    image_labels=image_labels
                )
                print(f"处理结果: {result}")
            except Exception as e:
                logger.error(f"处理图像 {img_path} 时出错: {e}")
                print(f"错误: {e}")