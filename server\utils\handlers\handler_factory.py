import logging
from typing import Dict, Type
from server.utils.handlers.base_handler import BaseFrameHandler


class HandlerFactory:
    """处理器工厂类
    
    根据system_type返回对应的处理器实例
    
    Attributes:
        processor: 主处理器实例
        handlers: 处理器映射字典
    """
    
    def __init__(self, processor):
        """初始化处理器工厂
        
        Args:
            processor: 主处理器实例
        """
        self.processor = processor
        self.handlers = {}
        self._register_handlers()
    
    def _register_handlers(self):
        """注册所有处理器
        
        将system_type映射到对应的处理器类
        """
        from server.utils.handlers.filter_handler import FilterHandler
        from server.utils.handlers.aerobic_pool_handler import AerobicPoolHandler
        from server.utils.handlers.bucket_dipper_handler import BucketDipperHandler
        from server.utils.handlers.bucket_shaft_handler import BucketShaftHandler
        from server.utils.handlers.moss_handler import Moss<PERSON>andler
        from server.utils.handlers.slag_outlet_handler import SlagOutletHandler
        from server.utils.handlers.holistic_perspective_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>ive<PERSON>and<PERSON>
        from server.utils.handlers.leaf_recognition_handler import LeafRecognitionHandler
        
        # 注册各种处理器
        # 滤池相关处理器
        self.register_handler('system_prompt_filter1', FilterHandler) # 滤池 1001
        self.register_handler('system_prompt_filter_multiple', FilterHandler) # 多图滤池 2001
        
        # 好氧池相关处理器
        self.register_handler('system_prompt_aerobic_single1', AerobicPoolHandler) # 单图好氧池-前段 1002
        self.register_handler('system_prompt_aerobic_multiple1', AerobicPoolHandler) # 多图好氧池-前段 2002
        self.register_handler('system_prompt_aerobic_single2', AerobicPoolHandler) # 单图好氧池-中段 1003
        self.register_handler('system_prompt_aerobic_multiple2', AerobicPoolHandler) # 多图好氧池-中段 2003
        
        # 耙斗相关处理器
        self.register_handler('system_prompt_bucket_dipper', BucketDipperHandler) # 耙斗-单图 1021
        self.register_handler('system_prompt_bucket_dipper_multiple', BucketDipperHandler) # 多图耙斗 2021
        # 耙斗井相关处理器
        self.register_handler('system_prompt_bucket_shaft', BucketShaftHandler) # 耙斗井 1322
        self.register_handler('system_prompt_bucket_shaft_multiple', BucketShaftHandler) # 多图耙斗井 2322
        
        # 青苔检测处理器
        self.register_handler('system_prompt_moss', MossHandler) # 青苔检测 1004
        
        # 泡沫检测处理器
        self.register_handler('system_prompt_slag_outlet', SlagOutletHandler) # 排浮渣识别 1005
        self.register_handler('system_prompt_slag_outletv2', SlagOutletHandler) # 排浮渣识别v2 1008
        
        # 二沉池整体视角分析处理器
        self.register_handler('system_prompt_holistic_perspective', HolisticPerspectiveHandler) # 二沉池整体视角分析 1006
        
        # 树叶识别处理器
        self.register_handler('system_prompt_leaf_recognition', LeafRecognitionHandler) # 树叶识别 1007
    
    def register_handler(self, system_type: str, handler_class: Type[BaseFrameHandler]):
        """注册处理器
        
        Args:
            system_type: 系统类型
            handler_class: 处理器类
        """
        self.handlers[system_type] = handler_class(self.processor)
        logging.info(f"注册处理器: {system_type} -> {handler_class.__name__}")
    
    def get_handler(self, system_type: str) -> BaseFrameHandler:
        """获取处理器
        
        Args:
            system_type: 系统类型
            
        Returns:
            BaseFrameHandler: 对应的处理器实例
            
        Raises:
            ValueError: 当没有找到对应的处理器时
        """
        handler = self.handlers.get(system_type)
        if not handler:
            # 尝试找到可能包含system_type的处理器
            for key, value in self.handlers.items():
                if key in system_type:
                    logging.info(f"未找到完全匹配的处理器 '{system_type}'，使用部分匹配的处理器 '{key}'")
                    return value
            
            logging.error(f"未找到处理器: {system_type}")
            raise ValueError(f"未找到对应的处理器: {system_type}")
        
        return handler 