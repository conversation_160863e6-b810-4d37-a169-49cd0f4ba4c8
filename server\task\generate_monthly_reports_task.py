#!/usr/bin/env python3
"""
批量生成所有设备的月报任务

该模块可以用于生成指定年月所有设备的月报，作为定时任务每月初执行
"""
import sys
import os
import argparse
import logging
from datetime import datetime, timedelta

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from server.utils.monthly_report_db import save_monthly_report, save_scene_monthly_report
from server.remote.device import get_device_scene_mapping
from server.utils.get_cameras_info import get_cameras_config
from server.utils.logger import setup_logging

# 配置日志
setup_logging()
logger = logging.getLogger(__name__)

def generate_all_monthly_reports(target_year=None, target_month=None, months_back=0):
    """
    为所有设备生成指定年月的月报
    
    Args:
        target_year (int, optional): 目标年份，默认为当前年份
        target_month (int, optional): 目标月份，默认为当前月份
        months_back (int, optional): 往前推算的月数，生成过去几个月的报告，默认为0
        
    Returns:
        list: 成功生成月报的设备ID列表
    """
    # 确定目标年月
    if target_year is None or target_month is None:
        # 如果没有指定目标年月，使用当前年月
        now = datetime.now()
        base_year = target_year or now.year
        base_month = target_month or now.month
    else:
        base_year = target_year
        base_month = target_month
    
    # 计算实际的报告年月
    target_date = datetime(base_year, base_month, 1)
    report_date = target_date - timedelta(days=months_back * 30)  # 简化的月份计算
    report_year = report_date.year
    report_month = report_date.month
    
    # 获取所有摄像头配置
    logger.info("开始获取摄像头配置信息...")
    cameras_config = get_cameras_config()
    camera_ids = [camera.get('camera_id') for camera in cameras_config if camera.get('camera_id')]
    
    logger.info(f"共找到 {len(camera_ids)} 个设备，将为 {report_year}年{report_month}月 生成月报")
    
    # 存储成功生成月报的设备ID
    successful_devices = []
    failed_devices = []
    
    # 为每个设备生成月报
    for i, camera_id in enumerate(camera_ids):
        logger.info(f"[{i+1}/{len(camera_ids)}] 正在为设备 {camera_id} 生成 {report_year}年{report_month}月 的月报...")
        try:
            report_id = save_monthly_report(camera_id, report_year, report_month)
            if report_id:
                logger.info(f"设备 {camera_id} 的月报生成成功，ID: {report_id}")
                successful_devices.append(camera_id)
            else:
                logger.error(f"设备 {camera_id} 的月报生成失败")
                failed_devices.append(camera_id)
        except Exception as e:
            logger.error(f"设备 {camera_id} 的月报生成过程中出错: {e}")
            failed_devices.append(camera_id)
    
    # 记录统计信息
    logger.info("===== 生成结果统计 =====")
    logger.info(f"年月: {report_year}年{report_month}月")
    logger.info(f"总设备数: {len(camera_ids)}")
    logger.info(f"成功生成: {len(successful_devices)}")
    logger.info(f"生成失败: {len(failed_devices)}")
    logger.info("===== 生成结果统计 =====")
    if failed_devices:
        logger.warning("以下设备的月报生成失败:")
        for device_id in failed_devices:
            logger.warning(f"  - {device_id}")
    
    # 如果有设备月报生成成功，则自动生成场景月报
    if successful_devices:
        logger.info("开始自动生成场景月报...")
        generate_scene_monthly_reports(report_year, report_month)
    
    return successful_devices


def generate_scene_monthly_reports(report_year, report_month):
    """
    生成所有场景的月报
    
    Args:
        report_year (int): 报告年份
        report_month (int): 报告月份
        
    Returns:
        dict: 生成结果统计 {"successful_scenes": [], "failed_scenes": []}
    """
    logger.info(f"开始生成场景月报，年月: {report_year}年{report_month}月")
    
    try:
        # 获取设备场景映射关系,字典的形式包含中英文的场景名称和设备列表
        scene_mapping = get_device_scene_mapping()
        
        if not scene_mapping:
            logger.warning("未获取到场景映射关系，跳过场景月报生成")
            return {"successful_scenes": [], "failed_scenes": []}
        
        successful_scenes = []
        failed_scenes = []
        
        logger.info(f"共找到 {len(scene_mapping)} 个场景，开始生成场景月报")
        
        # 为每个场景生成场景月报
        for i, (scene_id, scene_info) in enumerate(scene_mapping.items(), 1):
            scene_name = scene_info.get("scene_name", f"场景{scene_id[:8]}")
            devices = scene_info.get("devices", [])
            
            logger.info(f"[{i}/{len(scene_mapping)}] 正在为场景 {scene_id} ({scene_name}) 生成月报（包含 {len(devices)} 台设备）...")
            
            try:
                # 调用场景月报生成函数，直接传递设备列表和场景名称，避免重复调用接口
                scene_report_id = save_scene_monthly_report(
                    scene_id=scene_id,
                    scene_name=scene_name,  # 使用已获取的场景名称
                    scene_devices=devices,  # 直接传递设备列表，避免重复调用接口
                    report_year=report_year,
                    report_month=report_month,
                    submitter="AI巡检员",
                    import_to_knowledge_base=True
                )
                
                if scene_report_id:
                    logger.info(f"场景 {scene_id} 的月报生成成功，ID: {scene_report_id}")
                    successful_scenes.append(scene_id)
                else:
                    logger.warning(f"场景 {scene_id} 的月报生成失败")
                    failed_scenes.append(scene_id)
                    
            except Exception as e:
                logger.error(f"场景 {scene_id} 的月报生成过程中出错: {e}")
                failed_scenes.append(scene_id)
        
        # 记录场景月报生成统计信息
        logger.info("===== 场景月报生成结果统计 =====")
        logger.info(f"年月: {report_year}年{report_month}月")
        logger.info(f"总场景数: {len(scene_mapping)}")
        logger.info(f"成功生成: {len(successful_scenes)}")
        logger.info(f"生成失败: {len(failed_scenes)}")
        logger.info("===== 场景月报生成结果统计 =====")
        
        if failed_scenes:
            logger.warning("以下场景的月报生成失败:")
            for scene_id in failed_scenes:
                logger.warning(f"  - {scene_id}")
        
        return {
            "successful_scenes": successful_scenes,
            "failed_scenes": failed_scenes
        }
        
    except Exception as e:
        logger.error(f"场景月报批量生成过程中发生异常: {e}")
        return {"successful_scenes": [], "failed_scenes": []}


def start_monthly_reports_task():
    """
    定时任务入口函数，生成上个月的所有设备月报
    
    在app_run.py中作为定时任务每月1号执行
    """
    logger.info(f"开始执行设备月报生成任务，时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 默认生成上个月的月报
    now = datetime.now()
    if now.month == 1:
        # 如果是1月，则生成去年12月的报告
        target_year = now.year - 1
        target_month = 12
    else:
        # 否则生成上个月的报告
        target_year = now.year
        target_month = now.month - 1
    
    successful_devices = generate_all_monthly_reports(target_year, target_month)
    
    logger.info(f"设备月报生成任务完成，成功生成 {len(successful_devices)} 个设备的月报")
    logger.info("月报生成任务全部完成（包括设备月报和场景月报）")
    return successful_devices

def generate_current_month_reports():
    """
    生成当前月份的月报（用于测试或手动执行）
    """
    logger.info(f"开始生成当月设备月报，时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    now = datetime.now()
    successful_devices = generate_all_monthly_reports(now.year, now.month)
    
    logger.info(f"当月设备月报生成完成，成功生成 {len(successful_devices)} 个设备的月报")
    logger.info("当月月报生成任务全部完成（包括设备月报和场景月报）")
    return successful_devices

# 如果直接运行此脚本，则执行月报生成任务
if __name__ == "__main__":
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description="批量生成所有设备的月报")
    parser.add_argument("--year", "-y", type=int, help="目标年份，默认为当前年份")
    parser.add_argument("--month", "-m", type=int, help="目标月份，默认为当前月份")
    parser.add_argument("--months-back", "-b", type=int, default=0, help="往前推算的月数，生成过去几个月的报告，默认为0")
    parser.add_argument("--current", "-c", action="store_true", help="生成当前月份的月报")
    parser.add_argument("--last", "-l", action="store_true", help="生成上个月的月报（默认行为）")
    
    # 解析命令行参数
    # args = parser.parse_args()
    
    # if args.current:
    #     # 生成当前月份的月报
    #     generate_current_month_reports()
    # elif args.last:
    #     # 生成上个月的月报
    #     start_monthly_reports_task()
    # else:
    #     # 调用函数生成月报
    #     generate_all_monthly_reports(args.year, args.month, args.months_back)
    start_monthly_reports_task()
    # generate_current_month_reports()