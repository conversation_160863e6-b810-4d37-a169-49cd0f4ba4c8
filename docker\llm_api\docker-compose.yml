version: "3.3"

services:

  llm-api:
    container_name: llm-api
    image: opshub_llm-api:latest
    environment:
      - LANG=C.UTF-8
      - LANGUAGE=C.UTF-8
      - LC_ALL=C.UTF-8
    build:
      context: ../..  # 这里把当前路径的上一级加到上下文环境中
      dockerfile: ./docker/llm_api/Dockerfile  #指定dockerfile
    stdin_open: true
    tty: true
    restart: always  # 服务器重启后docker自启动
    ports:
      - '8201:18800'
    volumes:
      # 挂载项目文件（包含所有代码和配置文件）
      - ../../:/app/
      - ../../../configs:/app/configs
    logging:
      driver: "json-file"
      options:
        max-size: "20m"
        max-file: "30"