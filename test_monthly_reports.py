#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试月报和场景月报查询接口的修改
"""

import requests
import json
from datetime import datetime

# 测试服务器地址
BASE_URL = "http://localhost:8000"  # 根据实际情况修改

def test_monthly_reports():
    """测试月报查询接口"""
    print("=== 测试月报查询接口 ===")

    # 测试1: 不传任何参数，应该查询当前年份上个月的所有设备月报
    print("\n1. 测试默认查询（当前年份上个月）:")
    response = requests.post(f"{BASE_URL}/cam/query_monthly_reports", json={})
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"查询年份: {data['data']['query_year']}")
        print(f"查询月份: {data['data']['query_month']}")
        print(f"总记录数: {data['data']['total']}")
    else:
        print(f"错误: {response.text}")

    # 测试2: 指定年月查询
    print("\n2. 测试指定年月查询（2024年7月）:")
    response = requests.post(f"{BASE_URL}/cam/query_monthly_reports", json={
        "year": 2024,
        "month": 7
    })
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"查询年份: {data['data']['query_year']}")
        print(f"查询月份: {data['data']['query_month']}")
        print(f"总记录数: {data['data']['total']}")
    else:
        print(f"错误: {response.text}")

    # 测试3: 【重要】测试只传入年份的情况（修复验证）
    print("\n3. 测试只传入年份（验证修复）:")
    response = requests.post(f"{BASE_URL}/cam/query_monthly_reports", json={
        "year": 2024
    })
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"查询年份: {data['data']['query_year']} (应该是2024)")
        print(f"查询月份: {data['data']['query_month']} (应该是上个月)")
        print(f"总记录数: {data['data']['total']}")
        # 验证年份没有被错误覆盖
        if data['data']['query_year'] == 2024:
            print("✅ 修复成功：用户传入的年份没有被覆盖")
        else:
            print("❌ 修复失败：用户传入的年份被错误覆盖")
    else:
        print(f"错误: {response.text}")

    # 测试4: 指定设备ID查询
    print("\n4. 测试指定设备ID查询:")
    response = requests.post(f"{BASE_URL}/cam/query_monthly_reports", json={
        "camera_ids": ["4058", "4059"],
        "year": 2024,
        "month": 7
    })
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"查询年份: {data['data']['query_year']}")
        print(f"查询月份: {data['data']['query_month']}")
        print(f"总记录数: {data['data']['total']}")
        print(f"返回记录数: {len(data['data']['rows'])}")
    else:
        print(f"错误: {response.text}")

    # 测试5: 报告名称模糊查询
    print("\n5. 测试报告名称模糊查询:")
    response = requests.post(f"{BASE_URL}/cam/query_monthly_reports", json={
        "report_name": "巡检月报",
        "year": 2024,
        "month": 7
    })
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"查询年份: {data['data']['query_year']}")
        print(f"查询月份: {data['data']['query_month']}")
        print(f"总记录数: {data['data']['total']}")
    else:
        print(f"错误: {response.text}")

def test_monthly_reports_summary():
    """测试月报摘要查询接口"""
    print("\n=== 测试月报摘要查询接口 ===")
    
    response = requests.post(f"{BASE_URL}/cam/query_monthly_reports_summary", json={
        "year": 2024,
        "month": 7
    })
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"查询年份: {data['data']['query_year']}")
        print(f"查询月份: {data['data']['query_month']}")
        print(f"总记录数: {data['data']['total']}")
        print("摘要接口正常工作（不包含full_report字段）")
    else:
        print(f"错误: {response.text}")

def test_scene_monthly_reports():
    """测试场景月报查询接口"""
    print("\n=== 测试场景月报查询接口 ===")
    
    # 测试1: 不传任何参数，应该查询当前年份上个月的所有场景月报
    print("\n1. 测试默认查询（当前年份上个月）:")
    response = requests.post(f"{BASE_URL}/cam/query_scene_monthly_reports", json={})
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"查询年份: {data['data']['query_year']}")
        print(f"查询月份: {data['data']['query_month']}")
        print(f"总记录数: {data['data']['pagination']['total_count']}")
    else:
        print(f"错误: {response.text}")
    
    # 测试2: 指定场景ID查询
    print("\n2. 测试指定场景ID查询:")
    response = requests.post(f"{BASE_URL}/cam/query_scene_monthly_reports", json={
        "scene_ids": ["scene1", "scene2"],
        "year": 2024,
        "month": 7
    })
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"查询年份: {data['data']['query_year']}")
        print(f"查询月份: {data['data']['query_month']}")
        print(f"总记录数: {data['data']['pagination']['total_count']}")
    else:
        print(f"错误: {response.text}")

def test_scene_monthly_reports_summary():
    """测试场景月报摘要查询接口"""
    print("\n=== 测试场景月报摘要查询接口 ===")
    
    response = requests.post(f"{BASE_URL}/cam/query_scene_monthly_reports_summary", json={
        "year": 2024,
        "month": 7
    })
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"查询年份: {data['data']['query_year']}")
        print(f"查询月份: {data['data']['query_month']}")
        print(f"总记录数: {data['data']['pagination']['total_count']}")
        print("场景月报摘要接口正常工作（不包含大字段）")
    else:
        print(f"错误: {response.text}")

if __name__ == "__main__":
    print("开始测试月报和场景月报查询接口...")
    print(f"当前时间: {datetime.now()}")
    
    try:
        test_monthly_reports()
        test_monthly_reports_summary()
        test_scene_monthly_reports()
        test_scene_monthly_reports_summary()
        print("\n=== 测试完成 ===")
    except requests.exceptions.ConnectionError:
        print("错误: 无法连接到服务器，请确保服务器正在运行")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
