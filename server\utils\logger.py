import logging
from logging.handlers import RotatingFileHandler
from datetime import datetime
from pathlib import Path
from config_file import config
import sys
import threading

# 添加彩色日志支持
class ColoredFormatter(logging.Formatter):
    """为不同级别的日志添加颜色
    
    颜色对应:
    - DEBUG: 蓝色
    - INFO: 绿色
    - WARNING: 黄色
    - ERROR: 红色
    - CRITICAL: 紫色背景
    """
    
    # 终端颜色代码
    COLORS = {
        'DEBUG': '\033[94m',  # 蓝色
        'INFO': '\033[92m',   # 绿色
        'WARNING': '\033[93m', # 黄色
        'ERROR': '\033[91m',  # 红色
        'CRITICAL': '\033[45m', # 紫色背景
        'RESET': '\033[0m'    # 重置颜色
    }
    
    def format(self, record):
        """格式化日志记录，添加颜色"""
        # 保存原始的levelname
        original_levelname = record.levelname
        
        # 将日志级别格式化为固定宽度（7个字符），放在方括号内
        # 例如: [INFO   ], [WARNING], [DEBUG  ], [ERROR  ]
        padded_levelname = f"[{original_levelname:<7}]"
        
        # 仅在有颜色支持的终端添加颜色
        if hasattr(sys.stderr, 'isatty') and sys.stderr.isatty():
            if original_levelname in self.COLORS:
                colored_levelname = f"{self.COLORS[original_levelname]}{padded_levelname}{self.COLORS['RESET']}"
                record.levelname = colored_levelname
            else:
                record.levelname = padded_levelname
        else:
            record.levelname = padded_levelname
        
        result = super().format(record)
        
        # 恢复原始的levelname，避免影响其他格式化器
        record.levelname = original_levelname
        return result

class DailyPathRotatingFileHandler(RotatingFileHandler):
    """按日期路径轮转的文件处理器
    
    特点：
    1. 自动按年/月/日创建目录结构
    2. 每天的日志存储在对应的日期目录中
    3. 支持文件大小轮转
    """
    
    def __init__(self, base_path, filename, max_bytes, backup_count):
        """初始化处理器
        
        Args:
            base_path (Path): 日志基础路径
            filename (str): 日志文件名
            max_bytes (int): 单个日志文件最大字节数
            backup_count (int): 保留的备份文件数量
        """
        self.base_path = Path(base_path)
        self.filename = filename
        self._last_date = None
        self._lock = threading.RLock()  # 添加可重入锁保护并发访问
        
        # 初始化时创建首个日志文件
        initial_path = self._get_log_path()
        super().__init__(str(initial_path), maxBytes=max_bytes, backupCount=backup_count)

    def _get_log_path(self):
        """获取当前日期的日志路径"""
        current_date = datetime.now()
        date_path = self.base_path / \
                   str(current_date.year) / \
                   str(current_date.month).zfill(2) / \
                   str(current_date.day).zfill(2)
        
        # 创建日期目录
        date_path.mkdir(parents=True, exist_ok=True)
        return date_path / self.filename

    def emit(self, record):
        """发出日志记录
        
        在每次写日志时检查日期是否变化，如果变化则更新日志文件路径
        线程安全: 使用锁确保多线程环境下安全切换日志文件
        """
        # 使用线程锁保护关键代码段
        with self._lock:
            current_date = datetime.now().date()
            
            # 如果日期发生变化，更新日志文件路径
            if self._last_date != current_date:
                self._last_date = current_date
                
                # 关闭当前日志文件
                if self.stream:
                    self.stream.close()
                    self.stream = None
                
                # 更新日志文件路径
                new_path = self._get_log_path()
                self.baseFilename = str(new_path)
                
                # 重新打开新的日志文件
                if not self.stream and self.mode != 'w' and self.encoding:
                    self.stream = open(self.baseFilename, self.mode, encoding=self.encoding)
            
            # 实际记录日志也在锁保护下进行
            super().emit(record)

def setup_logging():
    """配置并初始化日志系统
    
    设置日志记录器，包括文件输出和控制台输出
    
    日志文件组织：
        按年/月/日的目录结构实时存储日志文件：
        logs/
          └── 2024/
              └── 03/
                  └── 21/
                      └── app.log
    """
    log_config = config.env['storage']['logging']
    log_path = Path(config.env['storage']['paths']['logs'])
    
    # 创建自定义的日志处理器
    handler = DailyPathRotatingFileHandler(
        base_path=log_path,
        filename=log_config['filename'],
        max_bytes=log_config['max_bytes'],
        backup_count=log_config['backup_count']
    )
    
    # 设置日志格式为: 2023-10-27 10:30:00 - [INFO   ] - 消息内容
    log_format = "%(asctime)s - %(levelname)s - %(message)s"
    date_format = "%Y-%m-%d %H:%M:%S"
    
    # 设置文件处理器的格式
    file_formatter = logging.Formatter(log_format, date_format)
    handler.setFormatter(file_formatter)
    
    # 获取根日志记录器并配置
    logger = logging.getLogger()
    logger.setLevel(getattr(logging, log_config['level']))
    
    # 移除现有的处理程序(如果有)
    for existing_handler in logger.handlers[:]:
        logger.removeHandler(existing_handler)
        
    # 添加文件处理程序
    logger.addHandler(handler)
    
    # 添加彩色控制台输出处理程序
    console_handler = logging.StreamHandler()
    colored_formatter = ColoredFormatter(log_format, date_format)
    console_handler.setFormatter(colored_formatter)
    logger.addHandler(console_handler) 