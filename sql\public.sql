/*
 Navicat Premium Dump SQL

 Source Server         : pg_5433
 Source Server Type    : PostgreSQL
 Source Server Version : 170004 (170004)
 Source Host           : localhost:5433
 Source Catalog        : GZe9_test
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 170004 (170004)
 File Encoding         : 65001

 Date: 13/04/2025 14:19:35
*/


-- ----------------------------
-- Sequence structure for frame_analysis_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."frame_analysis_id_seq";
CREATE SEQUENCE "public"."frame_analysis_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;
ALTER SEQUENCE "public"."frame_analysis_id_seq" OWNER TO "GZe9";

-- ----------------------------
-- Sequence structure for frame_analysis_id_seq1
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."frame_analysis_id_seq1";
CREATE SEQUENCE "public"."frame_analysis_id_seq1" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;
ALTER SEQUENCE "public"."frame_analysis_id_seq1" OWNER TO "GZe9";

-- ----------------------------
-- Sequence structure for frame_analysis_latest_id_frame_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."frame_analysis_latest_id_frame_seq";
CREATE SEQUENCE "public"."frame_analysis_latest_id_frame_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;
ALTER SEQUENCE "public"."frame_analysis_latest_id_frame_seq" OWNER TO "GZe9";

-- ----------------------------
-- Sequence structure for frame_analysis_latest_id_frame_seq1
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."frame_analysis_latest_id_frame_seq1";
CREATE SEQUENCE "public"."frame_analysis_latest_id_frame_seq1" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;
ALTER SEQUENCE "public"."frame_analysis_latest_id_frame_seq1" OWNER TO "GZe9";

-- ----------------------------
-- Table structure for frame_analysis
-- ----------------------------
DROP TABLE IF EXISTS "public"."frame_analysis";
CREATE TABLE "public"."frame_analysis" (
  "id" int8 NOT NULL GENERATED BY DEFAULT AS IDENTITY (
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1
),
  "camera_id" varchar(50) COLLATE "pg_catalog"."default",
  "video_id" varchar(100) COLLATE "pg_catalog"."default",
  "frame_number" int8,
  "timestamp" timestamp(6),
  "frame_path" text COLLATE "pg_catalog"."default",
  "coverage_rate" float8,
  "coverage_level" varchar(50) COLLATE "pg_catalog"."default",
  "alarm_status" varchar(10) COLLATE "pg_catalog"."default",
  "analysis_detail" text COLLATE "pg_catalog"."default",
  "adjustment_suggestion" text COLLATE "pg_catalog"."default",
  "is_abnormal" bool,
  "do_value" float8,
  "mlss_value" float8,
  "is_read" varchar(255) COLLATE "pg_catalog"."default" DEFAULT 0,
  "failure_reasons_type" jsonb,
  "failure_reasons_number" int4
)
;
ALTER TABLE "public"."frame_analysis" OWNER TO "GZe9";
COMMENT ON COLUMN "public"."frame_analysis"."camera_id" IS '摄像头唯一标识符，用于关联cameras表,对应设备ID';
COMMENT ON COLUMN "public"."frame_analysis"."video_id" IS '对应设备编号标识符';
COMMENT ON COLUMN "public"."frame_analysis"."frame_number" IS '视频中的帧序号，从0开始计数';
COMMENT ON COLUMN "public"."frame_analysis"."timestamp" IS '帧分析的时间戳，记录分析执行的具体时间';
COMMENT ON COLUMN "public"."frame_analysis"."frame_path" IS '帧图片在存储系统中的路径';
COMMENT ON COLUMN "public"."frame_analysis"."coverage_rate" IS '泡沫覆盖率，范围0.0-1.0，表示覆盖面积占比';
COMMENT ON COLUMN "public"."frame_analysis"."coverage_level" IS '覆盖等级：LOW, MEDIUM, HIGH';
COMMENT ON COLUMN "public"."frame_analysis"."alarm_status" IS '报警状态：0代表NO_ALARM, 1代表WARNING';
COMMENT ON COLUMN "public"."frame_analysis"."analysis_detail" IS 'JSON格式的详细分析结果，包含检测到的对象和其他分析数据';
COMMENT ON COLUMN "public"."frame_analysis"."adjustment_suggestion" IS '基于分析结果给出的调整建议和处理意见';
COMMENT ON COLUMN "public"."frame_analysis"."is_abnormal" IS '布尔值，表示是否检测到异常情况';
COMMENT ON COLUMN "public"."frame_analysis"."do_value" IS 'DO因子值，表示溶解氧含量';
COMMENT ON COLUMN "public"."frame_analysis"."mlss_value" IS 'MLSS因子值，表示混合液悬浮固体浓度';

-- ----------------------------
-- Table structure for frame_analysis_latest
-- ----------------------------
DROP TABLE IF EXISTS "public"."frame_analysis_latest";
CREATE TABLE "public"."frame_analysis_latest" (
  "id_frame" int8 NOT NULL GENERATED BY DEFAULT AS IDENTITY (
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1
),
  "camera_id" varchar(50) COLLATE "pg_catalog"."default",
  "video_id" varchar(50) COLLATE "pg_catalog"."default",
  "frame_number" int8,
  "timestamp" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
  "frame_path" text COLLATE "pg_catalog"."default",
  "coverage_rate" float8,
  "coverage_level" varchar(20) COLLATE "pg_catalog"."default",
  "alarm_status" varchar(20) COLLATE "pg_catalog"."default",
  "analysis_detail" text COLLATE "pg_catalog"."default",
  "is_abnormal" bool DEFAULT false,
  "do_value" float8,
  "mlss_value" float8,
  "adjustment_suggestion" text COLLATE "pg_catalog"."default",
  "is_read" varchar(255) COLLATE "pg_catalog"."default",
  "failure_reasons_type" jsonb,
  "failure_reasons_number" int4
)
;
ALTER TABLE "public"."frame_analysis_latest" OWNER TO "GZe9";
COMMENT ON COLUMN "public"."frame_analysis_latest"."camera_id" IS '摄像头唯一标识符，用于关联cameras表,对应设备ID';
COMMENT ON COLUMN "public"."frame_analysis_latest"."video_id" IS '对应设备编号标识符';
COMMENT ON COLUMN "public"."frame_analysis_latest"."frame_number" IS '视频中的帧序号，从0开始计数';
COMMENT ON COLUMN "public"."frame_analysis_latest"."timestamp" IS '帧分析的时间戳，记录分析执行的具体时间';
COMMENT ON COLUMN "public"."frame_analysis_latest"."frame_path" IS '帧图片在存储系统中的路径';
COMMENT ON COLUMN "public"."frame_analysis_latest"."coverage_rate" IS '泡沫覆盖率，范围0.0-1.0，表示覆盖面积占比';
COMMENT ON COLUMN "public"."frame_analysis_latest"."coverage_level" IS '覆盖等级：LOW, MEDIUM, HIGH';
COMMENT ON COLUMN "public"."frame_analysis_latest"."alarm_status" IS '报警状态：0代表NO_ALARM, 1代表WARNING';
COMMENT ON COLUMN "public"."frame_analysis_latest"."analysis_detail" IS 'JSON格式的详细分析结果，包含检测到的对象和其他分析数据';
COMMENT ON COLUMN "public"."frame_analysis_latest"."is_abnormal" IS '布尔值，表示是否检测到异常情况';
COMMENT ON COLUMN "public"."frame_analysis_latest"."do_value" IS 'DO因子值，表示溶解氧含量';
COMMENT ON COLUMN "public"."frame_analysis_latest"."mlss_value" IS 'MLSS因子值，表示混合液悬浮固体浓度';
COMMENT ON COLUMN "public"."frame_analysis_latest"."adjustment_suggestion" IS '基于分析结果给出的调整建议和处理意见';

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."frame_analysis_id_seq"
OWNED BY "public"."frame_analysis"."id";
SELECT setval('"public"."frame_analysis_id_seq"', 1506, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."frame_analysis_id_seq1"
OWNED BY "public"."frame_analysis"."id";
SELECT setval('"public"."frame_analysis_id_seq1"', 265, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."frame_analysis_latest_id_frame_seq"
OWNED BY "public"."frame_analysis_latest"."id_frame";
SELECT setval('"public"."frame_analysis_latest_id_frame_seq"', 392, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."frame_analysis_latest_id_frame_seq1"
OWNED BY "public"."frame_analysis_latest"."id_frame";
SELECT setval('"public"."frame_analysis_latest_id_frame_seq1"', 17, true);

-- ----------------------------
-- Indexes structure for table frame_analysis
-- ----------------------------
CREATE INDEX "idx_frame_analysis_camera_id" ON "public"."frame_analysis" USING btree (
  "camera_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_frame_analysis_coverage_rate" ON "public"."frame_analysis" USING btree (
  "coverage_rate" "pg_catalog"."float8_ops" ASC NULLS LAST
);
CREATE INDEX "idx_frame_analysis_is_abnormal" ON "public"."frame_analysis" USING btree (
  "is_abnormal" "pg_catalog"."bool_ops" ASC NULLS LAST
);
CREATE INDEX "idx_frame_analysis_timestamp" ON "public"."frame_analysis" USING btree (
  "timestamp" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table frame_analysis
-- ----------------------------
ALTER TABLE "public"."frame_analysis" ADD CONSTRAINT "frame_analysis_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Uniques structure for table frame_analysis_latest
-- ----------------------------
ALTER TABLE "public"."frame_analysis_latest" ADD CONSTRAINT "frame_analysis_latest_camera_id_key" UNIQUE ("camera_id");

-- ----------------------------
-- Primary Key structure for table frame_analysis_latest
-- ----------------------------
ALTER TABLE "public"."frame_analysis_latest" ADD CONSTRAINT "frame_analysis_latest_pkey" PRIMARY KEY ("id_frame");
