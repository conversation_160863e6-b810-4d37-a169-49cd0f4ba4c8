-- 创建场景月报表
CREATE TABLE IF NOT EXISTS scene_monthly_report (
    id SERIAL PRIMARY KEY,                            -- 主键ID
    scene_id VARCHAR(50) NOT NULL,                    -- 场景ID (对应sub_type)
    scene_name VARCHAR(100),                          -- 场景名称
    report_year INTEGER NOT NULL,                     -- 报告年份
    report_month INTEGER NOT NULL,                    -- 报告月份
    device_count INTEGER DEFAULT 0,                   -- 场景内设备数量
    device_list JSONB,                               -- 设备列表详情 [{"camera_id": "4058", "device_name": "设备1"}, ...]
    total_normal_count INTEGER DEFAULT 0,            -- 总正常记录数
    total_warning_count INTEGER DEFAULT 0,           -- 总告警记录数
    total_transition_count INTEGER DEFAULT 0,        -- 总状态转变记录数
    scene_analysis TEXT,                             -- 场景整体分析
    full_report TEXT,                                -- 场景完整报告
    summary_analysis TEXT,                           -- 场景总结分析
    raw_data JSONB,                                  -- 原始聚合数据
    main_failure_types JSONB,                        -- 场景主要故障类型统计JSON，聚合各设备的故障类型
    failure_type_summary TEXT,                       -- 场景故障类型摘要文本，基于所有设备故障类型的综合分析
    submitter VARCHAR(50) DEFAULT 'AI巡检员',        -- 提交人
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,  -- 创建时间
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,  -- 更新时间
    report_name VARCHAR(200) GENERATED ALWAYS AS (report_year::text || '年' || report_month::text || '月' || scene_name || '场景巡检月报') STORED, -- 场景月报名称
    UNIQUE (scene_id, report_year, report_month)     -- 场景ID、年份和月份的唯一约束
);

-- 索引
CREATE INDEX IF NOT EXISTS idx_scene_monthly_report_scene_id ON scene_monthly_report (scene_id);
CREATE INDEX IF NOT EXISTS idx_scene_monthly_report_year_month ON scene_monthly_report (report_year, report_month);
CREATE INDEX IF NOT EXISTS idx_scene_monthly_report_scene_name ON scene_monthly_report (scene_name);
CREATE INDEX IF NOT EXISTS idx_scene_monthly_report_report_name ON scene_monthly_report (report_name); -- 添加场景月报名称索引用于加速模糊查询

-- 注释
COMMENT ON TABLE scene_monthly_report IS '场景月报表，存储每个场景每月的聚合运行分析报告';
COMMENT ON COLUMN scene_monthly_report.id IS '主键ID';
COMMENT ON COLUMN scene_monthly_report.scene_id IS '场景ID，对应设备的sub_type字段';
COMMENT ON COLUMN scene_monthly_report.scene_name IS '场景名称';
COMMENT ON COLUMN scene_monthly_report.report_year IS '报告年份';
COMMENT ON COLUMN scene_monthly_report.report_month IS '报告月份';
COMMENT ON COLUMN scene_monthly_report.device_count IS '场景内设备数量';
COMMENT ON COLUMN scene_monthly_report.device_list IS '设备列表详情，JSON格式存储设备ID和名称等信息';
COMMENT ON COLUMN scene_monthly_report.total_normal_count IS '场景内所有设备的总正常记录数';
COMMENT ON COLUMN scene_monthly_report.total_warning_count IS '场景内所有设备的总告警记录数';
COMMENT ON COLUMN scene_monthly_report.total_transition_count IS '场景内所有设备的总状态转变记录数';
COMMENT ON COLUMN scene_monthly_report.scene_analysis IS '场景整体分析文本（基于所有设备的综合分析）';
COMMENT ON COLUMN scene_monthly_report.full_report IS '场景完整报告文本（大模型生成）';
COMMENT ON COLUMN scene_monthly_report.summary_analysis IS '场景报告总结（对完整报告的总结分析）';
COMMENT ON COLUMN scene_monthly_report.raw_data IS '原始聚合数据JSON，包括各设备的原始月报数据';
COMMENT ON COLUMN scene_monthly_report.main_failure_types IS '场景主要故障类型统计JSON，聚合各设备的故障类型';
COMMENT ON COLUMN scene_monthly_report.failure_type_summary IS '场景故障类型摘要文本，基于所有设备故障类型的综合分析';
COMMENT ON COLUMN scene_monthly_report.created_at IS '创建时间';
COMMENT ON COLUMN scene_monthly_report.updated_at IS '更新时间';
COMMENT ON COLUMN scene_monthly_report.submitter IS '提交人';
COMMENT ON COLUMN scene_monthly_report.report_name IS '场景月报名称，格式为"年+月+场景名称+场景巡检月报"';

-- 为已存在的表添加report_name列（如果表已经存在且需要更新）
ALTER TABLE scene_monthly_report ADD COLUMN IF NOT EXISTS report_name VARCHAR(200) 
GENERATED ALWAYS AS (report_year::text || '年' || 
                    report_month::text || '月' || 
                    scene_name || '场景巡检月报') STORED;

-- 为已存在的表添加submitter列（如果表已经存在且需要更新）
ALTER TABLE scene_monthly_report ADD COLUMN IF NOT EXISTS submitter VARCHAR(50) DEFAULT 'AI巡检员';

-- 为已存在的表添加summary_analysis列（如果表已经存在且需要更新）
ALTER TABLE scene_monthly_report ADD COLUMN IF NOT EXISTS summary_analysis TEXT;

-- 为已存在的表添加故障类型相关字段（如果表已经存在且需要更新）
ALTER TABLE scene_monthly_report ADD COLUMN IF NOT EXISTS main_failure_types JSONB;
ALTER TABLE scene_monthly_report ADD COLUMN IF NOT EXISTS failure_type_summary TEXT;
